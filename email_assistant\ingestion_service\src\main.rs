use std::fs;
use std::io;
use uuid::Uuid;
use chrono::{DateTime, Utc};
use serde::{Serialize, Deserialize};

// Email parsing and cleaning functions
pub fn html_to_plain_text(html: &str) -> String {
    html2text::from_read(html.as_bytes(), 80)
}

pub fn strip_signatures_and_quotes(text: &str) -> String {
    let mut cleaned_lines = Vec::new();
    let mut in_signature = false;

    for line in text.lines() {
        if line.trim() == "--" || (line.trim().starts_with("-- ") && line.trim().len() < 50) {
            in_signature = true;
            break; // Stop processing once we hit a signature
        }
        if line.starts_with('>') {
            continue;
        }
        cleaned_lines.push(line);
    }
    cleaned_lines.join("\n")
}

// Email struct for parsed emails
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct ParsedEmail {
    pub id: Uuid,
    pub subject: Option<String>,
    pub from: Option<String>,
    pub to: Vec<String>,
    pub sent_date: Option<DateTime<Utc>>,
    pub plain_text_body_raw: Option<String>,
    pub html_body_raw: Option<String>,
    pub cleaned_plain_text_body: Option<String>,
}

/// Parses a single .eml file into a ParsedEmail struct.
/// Applies cleaning functions and handles errors.
pub fn parse_eml(file_path: &str) -> Result<ParsedEmail, io::Error> {
    let content = fs::read_to_string(file_path)?;

    // Simple email parsing - extract basic headers and body
    let mut subject = None;
    let mut from = None;
    let mut to = Vec::new();
    let mut body_start = 0;

    for (i, line) in content.lines().enumerate() {
        if line.is_empty() {
            body_start = i + 1;
            break;
        }

        if line.to_lowercase().starts_with("subject:") {
            subject = Some(line[8..].trim().to_string());
        } else if line.to_lowercase().starts_with("from:") {
            from = Some(line[5..].trim().to_string());
        } else if line.to_lowercase().starts_with("to:") {
            to.push(line[3..].trim().to_string());
        }
    }

    let body: String = content.lines().skip(body_start).collect::<Vec<_>>().join("\n");
    let cleaned_body = strip_signatures_and_quotes(&body);

    Ok(ParsedEmail {
        id: Uuid::new_v4(),
        subject,
        from,
        to,
        sent_date: None, // Will implement proper date parsing later
        plain_text_body_raw: Some(body.clone()),
        html_body_raw: None,
        cleaned_plain_text_body: Some(cleaned_body),
    })
}

fn main() {
    println!("AI Email Assistant - Ingestion Service");
    println!("Email parsing functionality ready!");

    // Example usage (commented out for now)
    // match parse_eml("sample.eml") {
    //     Ok(email) => println!("Parsed email: {:?}", email),
    //     Err(e) => eprintln!("Error parsing email: {}", e),
    // }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::io::Write;
    use tempfile::NamedTempFile;

    #[test]
    fn test_html_to_plain_text() {
        let html_content = "<html><body><h1>Hello</h1><p>This is <b>HTML</b> with a <a href=\"http://example.com\">link</a>.</p><ul><li>Item 1</li><li>Item 2</li></ul></body></html>";
        let plain_text = html_to_plain_text(html_content);
        assert!(plain_text.contains("Hello"));
        assert!(plain_text.contains("HTML"));
        assert!(plain_text.contains("Item 1"));
        assert!(plain_text.contains("Item 2"));
    }

    #[test]
    fn test_strip_signatures_and_quotes() {
        let text_with_signature = "Hello,\n\nThis is the main body.\n\n-- \nJohn Doe\nSoftware Engineer";
        let cleaned = strip_signatures_and_quotes(text_with_signature);
        assert_eq!(cleaned, "Hello,\n\nThis is the main body.\n");

        let text_with_quote = "Hello,\n\nMy reply.\n\n> On Mon, Jan 1, 2023 at 10:00 AM John Doe <<EMAIL>> wrote:\n> This is a quoted message.";
        let cleaned_quote = strip_signatures_and_quotes(text_with_quote);
        assert_eq!(cleaned_quote, "Hello,\n\nMy reply.\n");
    }

    #[test]
    fn test_parse_valid_eml() -> Result<(), Box<dyn std::error::Error>> {
        let eml_content = "From: <EMAIL>\nSubject: Test Subject\nTo: <EMAIL>\n\nHello, this is a test email.";
        let mut file = NamedTempFile::new()?;
        file.write_all(eml_content.as_bytes())?;
        let file_path = file.path().to_str().unwrap();

        let parsed_email = parse_eml(file_path)?;
        assert_eq!(parsed_email.subject, Some("Test Subject".to_string()));
        assert_eq!(parsed_email.from, Some("<EMAIL>".to_string()));
        assert_eq!(parsed_email.to, vec!["<EMAIL>".to_string()]);
        assert!(parsed_email.cleaned_plain_text_body.is_some());
        // Note: Date parsing not implemented in simplified version
        Ok(())
    }

    #[test]
    fn test_parse_eml_with_html_body() -> Result<(), Box<dyn std::error::Error>> {
        let eml_content = "From: <EMAIL>\nSubject: HTML Test\nTo: <EMAIL>\nContent-Type: text/html\n\n<html><body><h1>Hello</h1><p>This is <b>HTML</b>.</p></body></html>";
        let mut file = NamedTempFile::new()?;
        file.write_all(eml_content.as_bytes())?;
        let file_path = file.path().to_str().unwrap();

        let parsed_email = parse_eml(file_path)?;
        assert_eq!(parsed_email.subject, Some("HTML Test".to_string()));
        // Note: HTML parsing not implemented in simplified version
        assert!(parsed_email.plain_text_body_raw.is_some());
        assert!(parsed_email.cleaned_plain_text_body.is_some());
        Ok(())
    }

    #[test]
    fn test_parse_invalid_eml() {
        let eml_content = "This is not a valid email format.";
        let mut file = NamedTempFile::new().unwrap();
        file.write_all(eml_content.as_bytes()).unwrap();
        let file_path = file.path().to_str().unwrap();

        let result = parse_eml(file_path);
        // Note: Simplified parser doesn't validate format, just extracts what it can
        assert!(result.is_ok());
        let parsed = result.unwrap();
        assert!(parsed.subject.is_none());
        assert!(parsed.from.is_none());
    }

    #[test]
    fn test_parse_non_existent_eml() {
        let result = parse_eml("non_existent_file.eml");
        assert!(result.is_err());
        let err = result.unwrap_err();
        assert_eq!(err.kind(), io::ErrorKind::NotFound);
    }
}
