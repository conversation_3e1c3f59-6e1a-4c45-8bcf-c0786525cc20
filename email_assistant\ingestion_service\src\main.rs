// AI-Assisted Email Response System - Ingestion Service Main
// Command-line interface for the email ingestion service

use ingestion_service::*;
use std::fs;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("AI Email Assistant - Ingestion Service");
    
    let args: Vec<String> = std::env::args().collect();
    
    // Establish database connection
    let pool = match establish_connection().await {
        Ok(pool) => {
            println!("✓ Database connection established");
            pool
        }
        Err(e) => {
            eprintln!("⚠ Database connection failed: {}", e);
            eprintln!("  Make sure PostgreSQL is running and configured correctly");
            return Err(e.into());
        }
    };
    
    // Run migrations
    if let Err(e) = run_migrations(&pool).await {
        eprintln!("Warning: Migration failed: {}", e);
    } else {
        println!("✓ Database migrations completed");
    }
    
    // Process command line arguments
    if args.len() > 1 {
        match args[1].as_str() {
            "ingest" => {
                if args.len() < 3 {
                    eprintln!("Usage: {} ingest <file_or_directory_path>", args[0]);
                    return Ok(());
                }
                
                let path = &args[2];
                if fs::metadata(path)?.is_dir() {
                    ingest_directory(&pool, path).await?;
                } else if path.ends_with(".mbox") {
                    ingest_mbox_file(&pool, path).await?;
                } else {
                    ingest_email_file(&pool, path).await?;
                }
            }
            "list" => {
                let messages = get_messages(&pool, Some(10)).await?;
                println!("Recent messages:");
                for message in messages {
                    println!("  {} - {} ({})", 
                        message.id, 
                        message.subject.unwrap_or_else(|| "No subject".to_string()),
                        message.from_address.unwrap_or_else(|| "Unknown sender".to_string())
                    );
                }
            }
            "test" => {
                println!("✓ Email parsing functionality ready!");
                println!("✓ Database connection working");
                println!("✓ All systems operational");
            }
            _ => {
                println!("Available commands:");
                println!("  ingest <file_or_directory> - Ingest emails from file or directory");
                println!("  list                       - List recent messages");
                println!("  test                       - Test system functionality");
            }
        }
    } else {
        println!("✓ Email parsing functionality ready!");
        println!("✓ Database setup complete");
        println!("Use 'cargo run -- help' for available commands");
    }
    
    Ok(())
}
