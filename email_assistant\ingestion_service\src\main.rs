// AI-Assisted Email Response System - Ingestion Service Main
// Command-line interface for the email ingestion service

use ingestion_service::*;
use std::fs;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("AI Email Assistant - Ingestion Service");

    let args: Vec<String> = std::env::args().collect();

    // Check if this is a test-embedding command (doesn't need database)
    if args.len() > 1 && args[1] == "test-embedding" {
        let embedding_service_url = std::env::var("EMBEDDING_SERVICE_URL")
            .unwrap_or_else(|_| "http://localhost:8003".to_string());

        println!("Testing embedding service connection...");
        match get_embedding("This is a test email for embedding generation.", &embedding_service_url).await {
            Ok(embedding) => {
                println!("✓ Embedding service working! Generated embedding with {} dimensions", embedding.len());
                println!("  First 5 values: {:?}", &embedding[..5.min(embedding.len())]);
            }
            Err(e) => {
                eprintln!("✗ Embedding service test failed: {}", e);
                eprintln!("  Make sure the embedding service is running on {}", embedding_service_url);
            }
        }
        return Ok(());
    }

    // Establish database connection for other commands
    let pool = match establish_connection().await {
        Ok(pool) => {
            println!("✓ Database connection established");
            pool
        }
        Err(e) => {
            eprintln!("⚠ Database connection failed: {}", e);
            eprintln!("  Make sure PostgreSQL is running and configured correctly");
            return Err(e.into());
        }
    };

    // Run migrations
    if let Err(e) = run_migrations(&pool).await {
        eprintln!("Warning: Migration failed: {}", e);
    } else {
        println!("✓ Database migrations completed");
    }
    
    // Process command line arguments
    if args.len() > 1 {
        match args[1].as_str() {
            "ingest" => {
                if args.len() < 3 {
                    eprintln!("Usage: {} ingest <file_or_directory_path> [--with-embeddings]", args[0]);
                    return Ok(());
                }

                let path = &args[2];
                let with_embeddings = args.len() > 3 && args[3] == "--with-embeddings";

                if with_embeddings {
                    let embedding_service_url = std::env::var("EMBEDDING_SERVICE_URL")
                        .unwrap_or_else(|_| "http://localhost:8003".to_string());

                    if fs::metadata(path)?.is_dir() {
                        // For directory, process each file individually with embeddings
                        for entry in fs::read_dir(path)? {
                            let entry = entry?;
                            let file_path = entry.path();

                            if let Some(extension) = file_path.extension() {
                                if extension == "eml" {
                                    if let Some(path_str) = file_path.to_str() {
                                        match ingest_email_file_with_embeddings(&pool, path_str, &embedding_service_url).await {
                                            Ok((msg_id, emb_id)) => {
                                                println!("✓ Processed {} - Message: {}, Embedding: {:?}", path_str, msg_id, emb_id);
                                            }
                                            Err(e) => eprintln!("Warning: Failed to process {}: {}", path_str, e),
                                        }
                                    }
                                } else if extension == "mbox" {
                                    if let Some(path_str) = file_path.to_str() {
                                        match ingest_mbox_file_with_embeddings(&pool, path_str, &embedding_service_url).await {
                                            Ok(results) => {
                                                println!("✓ Processed mbox {} - {} emails", path_str, results.len());
                                            }
                                            Err(e) => eprintln!("Warning: Failed to process {}: {}", path_str, e),
                                        }
                                    }
                                }
                            }
                        }
                    } else if path.ends_with(".mbox") {
                        ingest_mbox_file_with_embeddings(&pool, path, &embedding_service_url).await?;
                    } else {
                        ingest_email_file_with_embeddings(&pool, path, &embedding_service_url).await?;
                    }
                } else {
                    // Original functionality without embeddings
                    if fs::metadata(path)?.is_dir() {
                        ingest_directory(&pool, path).await?;
                    } else if path.ends_with(".mbox") {
                        ingest_mbox_file(&pool, path).await?;
                    } else {
                        ingest_email_file(&pool, path).await?;
                    }
                }
            }
            "list" => {
                let messages = get_messages(&pool, Some(10)).await?;
                println!("Recent messages:");
                for message in messages {
                    println!("  {} - {} ({})", 
                        message.id, 
                        message.subject.unwrap_or_else(|| "No subject".to_string()),
                        message.from_address.unwrap_or_else(|| "Unknown sender".to_string())
                    );
                }
            }
            "test" => {
                println!("✓ Email parsing functionality ready!");
                println!("✓ Database connection working");
                println!("✓ All systems operational");
            }

            _ => {
                println!("Available commands:");
                println!("  ingest <file_or_directory> [--with-embeddings] - Ingest emails from file or directory");
                println!("  list                                           - List recent messages");
                println!("  test                                           - Test system functionality");
                println!("  test-embedding                                 - Test embedding service connection");
                println!("");
                println!("Examples:");
                println!("  cargo run -- ingest sample.eml");
                println!("  cargo run -- ingest sample.eml --with-embeddings");
                println!("  cargo run -- ingest emails_directory --with-embeddings");
            }
        }
    } else {
        println!("✓ Email parsing functionality ready!");
        println!("✓ Database setup complete");
        println!("Use 'cargo run -- help' for available commands");
    }
    
    Ok(())
}
