{"rustc": 1842507548689473721, "features": "[]", "declared_features": "[]", "target": 4578055519624627116, "profile": 17672942494452627365, "path": 4942398508502643691, "deps": [[722509084658410873, "ingestion_service", false, 2214623351036593056], [1441306149310335789, "tempfile", false, 2701416769734930353], [1760623714118191065, "dotenv", false, 4035044196166406372], [3445762082367621908, "qdrant_client", false, 13097216553728445839], [7244058819997729774, "reqwest", false, 1326957071549275717], [8319709847752024821, "uuid", false, 10694262273986330983], [9689903380558560274, "serde", false, 694819115080761911], [9897246384292347999, "chrono", false, 13900098511327864438], [12393800526703971956, "tokio", false, 6137508459591548543], [15367738274754116744, "serde_json", false, 10117049250602624439], [17565085470850576164, "html2text", false, 10593824217524924029]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\ingestion_service-a8d72e09c56017cc\\dep-bin-ingestion_service", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}