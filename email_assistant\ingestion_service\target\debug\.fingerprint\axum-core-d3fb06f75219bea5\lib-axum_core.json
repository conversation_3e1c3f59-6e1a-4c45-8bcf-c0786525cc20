{"rustc": 1842507548689473721, "features": "[]", "declared_features": "[\"__private_docs\", \"tracing\"]", "target": 2565713999752801252, "profile": 15657897354478470176, "path": 13067367211683519187, "deps": [[784494742817713399, "tower_service", false, 11660862420847627360], [1906322745568073236, "pin_project_lite", false, 8963179910468966138], [2517136641825875337, "sync_wrapper", false, 11531262691852108096], [7712452662827335977, "tower_layer", false, 8862439362659442001], [7858942147296547339, "rustversion", false, 18095137511007975503], [9010263965687315507, "http", false, 15629939712500996649], [10229185211513642314, "mime", false, 12149313404929737718], [10629569228670356391, "futures_util", false, 2578735737630259245], [11946729385090170470, "async_trait", false, 12880380351208011548], [14084095096285906100, "http_body", false, 13962944359390943063], [16066129441945555748, "bytes", false, 2626033548966229242], [16900715236047033623, "http_body_util", false, 10967508203520950487]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\axum-core-d3fb06f75219bea5\\dep-lib-axum_core", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}