{"rustc": 1842507548689473721, "features": "[\"_rt-tokio\", \"_tls-rustls\", \"any\", \"chrono\", \"crc\", \"default\", \"json\", \"migrate\", \"offline\", \"rustls\", \"rustls-pemfile\", \"serde\", \"serde_json\", \"sha2\", \"tokio\", \"tokio-stream\", \"uuid\", \"webpki-roots\"]", "declared_features": "[\"_rt-async-std\", \"_rt-tokio\", \"_tls-native-tls\", \"_tls-none\", \"_tls-rustls\", \"any\", \"async-io\", \"async-std\", \"bigdecimal\", \"bit-vec\", \"bstr\", \"chrono\", \"crc\", \"default\", \"digest\", \"encoding_rs\", \"ipnetwork\", \"json\", \"mac_address\", \"migrate\", \"native-tls\", \"num-bigint\", \"offline\", \"regex\", \"rust_decimal\", \"rustls\", \"rustls-pemfile\", \"serde\", \"serde_json\", \"sha1\", \"sha2\", \"time\", \"tokio\", \"tokio-stream\", \"uuid\", \"webpki-roots\"]", "target": 2042750936636613814, "profile": 15657897354478470176, "path": 11940386072098527829, "deps": [[5103565458935487, "futures_io", false, 16925723084439930614], [40386456601120721, "percent_encoding", false, 4129156245155979692], [530211389790465181, "hex", false, 9910414242004828434], [788558663644978524, "crossbeam_queue", false, 2587100228992671788], [966925859616469517, "ahash", false, 6644871447749944679], [1162433738665300155, "crc", false, 16586469179183388588], [1464803193346256239, "event_listener", false, 14065357131839841849], [1811549171721445101, "futures_channel", false, 16975146098111304321], [3150220818285335163, "url", false, 12126118858251168585], [3405817021026194662, "hashlink", false, 9251688824475961552], [3646857438214563691, "futures_intrusive", false, 17546478229843708382], [3666196340704888985, "smallvec", false, 15952089557007962232], [3712811570531045576, "byteorder", false, 8373284891153564586], [3722963349756955755, "once_cell", false, 14521976164538839542], [5986029879202738730, "log", false, 5047229584628730050], [6493259146304816786, "indexmap", false, 4222083709158668202], [7620660491849607393, "futures_core", false, 5897199496535336083], [8008191657135824715, "thiserror", false, 9734261248633251398], [8319709847752024821, "uuid", false, 8393667855764434578], [8606274917505247608, "tracing", false, 3307036962754664465], [9689903380558560274, "serde", false, 16546253847386255219], [9857275760291862238, "sha2", false, 8159000495411279711], [9897246384292347999, "chrono", false, 3274416845873686043], [10629569228670356391, "futures_util", false, 8050287300830996249], [10862088793507253106, "sqlformat", false, 10521784746383090244], [11295624341523567602, "rustls", false, 5752837998231473873], [12170264697963848012, "either", false, 10799552135219786513], [12393800526703971956, "tokio", false, 9315860584743143408], [15367738274754116744, "serde_json", false, 2903224897898545405], [15932120279885307830, "memchr", false, 10200081116297473958], [16066129441945555748, "bytes", false, 2626033548966229242], [16311359161338405624, "rustls_pemfile", false, 18134819309736928621], [16973251432615581304, "tokio_stream", false, 10843962007771538114], [17106256174509013259, "atoi", false, 13633839343691877831], [17605717126308396068, "paste", false, 18420332913555425673], [17652733826348741533, "webpki_roots", false, 3179603548997442776]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\sqlx-core-958b983e3c821735\\dep-lib-sqlx_core", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}