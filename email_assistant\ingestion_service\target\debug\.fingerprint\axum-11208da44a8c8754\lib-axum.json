{"rustc": 1842507548689473721, "features": "[]", "declared_features": "[\"__private_docs\", \"default\", \"form\", \"http1\", \"http2\", \"json\", \"macros\", \"matched-path\", \"multipart\", \"original-uri\", \"query\", \"tokio\", \"tower-log\", \"tracing\", \"ws\"]", "target": 13920321295547257648, "profile": 2241668132362809309, "path": 1047557593175177085, "deps": [[40386456601120721, "percent_encoding", false, 15092931327459917350], [784494742817713399, "tower_service", false, 891412978182292050], [1906322745568073236, "pin_project_lite", false, 27178801118851068], [2517136641825875337, "sync_wrapper", false, 15079274337327401480], [4359148418957042248, "axum_core", false, 1508685593915654832], [5695049318159433696, "tower", false, 8541137164939621937], [7695812897323945497, "itoa", false, 6334248950938607083], [7712452662827335977, "tower_layer", false, 14062717672081574200], [7858942147296547339, "rustversion", false, 18095137511007975503], [9010263965687315507, "http", false, 4349961928876268954], [9678799920983747518, "matchit", false, 9375679574052841123], [9689903380558560274, "serde", false, 694819115080761911], [10229185211513642314, "mime", false, 2311114312296159829], [10629569228670356391, "futures_util", false, 1407185829515809506], [11946729385090170470, "async_trait", false, 12880380351208011548], [14084095096285906100, "http_body", false, 14653673679686981800], [15932120279885307830, "memchr", false, 3753662393507526803], [16066129441945555748, "bytes", false, 14757824830115038577], [16900715236047033623, "http_body_util", false, 10290862317673768159]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\axum-11208da44a8c8754\\dep-lib-axum", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}