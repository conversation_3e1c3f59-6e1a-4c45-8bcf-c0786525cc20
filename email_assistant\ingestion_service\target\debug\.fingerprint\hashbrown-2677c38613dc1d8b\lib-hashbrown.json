{"rustc": 1842507548689473721, "features": "[\"ahash\", \"allocator-api2\", \"default\", \"inline-more\"]", "declared_features": "[\"ahash\", \"alloc\", \"allocator-api2\", \"compiler_builtins\", \"core\", \"default\", \"equivalent\", \"inline-more\", \"nightly\", \"raw\", \"rayon\", \"rkyv\", \"rustc-dep-of-std\", \"rustc-internal-api\", \"serde\"]", "target": 9101038166729729440, "profile": 15657897354478470176, "path": 2487185053407325305, "deps": [[966925859616469517, "ahash", false, 6644871447749944679], [9150530836556604396, "allocator_api2", false, 6088146111808297415]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\hashbrown-2677c38613dc1d8b\\dep-lib-hashbrown", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}