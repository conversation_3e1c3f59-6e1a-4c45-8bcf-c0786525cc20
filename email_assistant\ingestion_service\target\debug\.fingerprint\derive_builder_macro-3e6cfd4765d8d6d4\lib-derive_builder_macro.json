{"rustc": 1842507548689473721, "features": "[\"lib_has_std\"]", "declared_features": "[\"alloc\", \"clippy\", \"lib_has_std\"]", "target": 15229808779680689443, "profile": 2225463790103693989, "path": 5937674952582547309, "deps": [[4003231138667150418, "derive_builder_core", false, 10786742377195760779], [4974441333307933176, "syn", false, 12057608429065043436]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\derive_builder_macro-3e6cfd4765d8d6d4\\dep-lib-derive_builder_macro", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}