{"rustc": 1842507548689473721, "features": "[\"alloc\", \"futures-io\", \"futures-sink\", \"io\", \"memchr\", \"sink\", \"slab\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"bilock\", \"cfg-target-has-atomic\", \"channel\", \"compat\", \"default\", \"futures-channel\", \"futures-io\", \"futures-macro\", \"futures-sink\", \"futures_01\", \"io\", \"io-compat\", \"memchr\", \"portable-atomic\", \"sink\", \"slab\", \"std\", \"tokio-io\", \"unstable\", \"write-all-vectored\"]", "target": 1788798584831431502, "profile": 13318305459243126790, "path": 3303257178207977090, "deps": [[5103565458935487, "futures_io", false, 16925723084439930614], [1615478164327904835, "pin_utils", false, 14445859001125264220], [1906322745568073236, "pin_project_lite", false, 8963179910468966138], [5451793922601807560, "slab", false, 12287450701618962364], [7013762810557009322, "futures_sink", false, 15039158409219223048], [7620660491849607393, "futures_core", false, 5897199496535336083], [15932120279885307830, "memchr", false, 10200081116297473958], [16240732885093539806, "futures_task", false, 4994500045102263713]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\futures-util-94fb424851d0e411\\dep-lib-futures_util", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}