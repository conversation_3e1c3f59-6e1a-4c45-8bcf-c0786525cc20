# AI Email Assistant - Service Startup Script
# This script starts the embedding service and provides commands for the ingestion service

param(
    [switch]$EmbeddingOnly,
    [switch]$Help
)

if ($Help) {
    Write-Host "AI Email Assistant - Service Startup Script"
    Write-Host ""
    Write-Host "Usage:"
    Write-Host "  .\start_services.ps1                 - Start embedding service and show ingestion commands"
    Write-Host "  .\start_services.ps1 -EmbeddingOnly  - Start only the embedding service"
    Write-Host "  .\start_services.ps1 -Help           - Show this help message"
    Write-Host ""
    Write-Host "Available ingestion commands (run in separate terminal):"
    Write-Host "  cd ingestion_service"
    Write-Host "  cargo run -- test-embedding                           # Test embedding service"
    Write-Host "  cargo run -- ingest test_email.eml --with-embeddings  # Ingest single email with embeddings"
    Write-Host "  cargo run -- list                                     # List recent messages"
    exit 0
}

Write-Host "=== AI Email Assistant - Service Manager ===" -ForegroundColor Green
Write-Host ""

# Check if we're in the right directory
if (-not (Test-Path "rag_service\main.py")) {
    Write-Host "Error: Please run this script from the email_assistant directory" -ForegroundColor Red
    exit 1
}

# Start embedding service
Write-Host "Starting embedding service..." -ForegroundColor Yellow
$embeddingJob = Start-Job -ScriptBlock {
    Set-Location $using:PWD
    Set-Location rag_service
    python main.py
}

# Wait a moment for the service to start
Start-Sleep -Seconds 3

# Test if embedding service is running
try {
    $response = Invoke-RestMethod -Uri "http://localhost:8003/health" -Method Get -TimeoutSec 5
    if ($response.status -eq "healthy") {
        Write-Host "✓ Embedding service started successfully" -ForegroundColor Green
    } else {
        Write-Host "⚠ Embedding service started but not healthy" -ForegroundColor Yellow
    }
} catch {
    Write-Host "⚠ Embedding service may still be starting..." -ForegroundColor Yellow
}

if ($EmbeddingOnly) {
    Write-Host ""
    Write-Host "Embedding service is running. Press Ctrl+C to stop." -ForegroundColor Cyan
    try {
        Wait-Job $embeddingJob
    } finally {
        Stop-Job $embeddingJob
        Remove-Job $embeddingJob
    }
    exit 0
}

Write-Host ""
Write-Host "=== Services Status ===" -ForegroundColor Cyan
Write-Host "✓ Embedding Service: http://localhost:8003" -ForegroundColor Green
Write-Host ""
Write-Host "=== Available Commands ===" -ForegroundColor Cyan
Write-Host "Run these commands in a new terminal window:" -ForegroundColor White
Write-Host ""
Write-Host "  cd ingestion_service" -ForegroundColor Gray
Write-Host "  cargo run -- test-embedding                           # Test embedding service" -ForegroundColor Gray
Write-Host "  cargo run -- ingest test_email.eml --with-embeddings  # Ingest single email with embeddings" -ForegroundColor Gray
Write-Host "  cargo run -- list                                     # List recent messages" -ForegroundColor Gray
Write-Host ""
Write-Host "Press Ctrl+C to stop all services" -ForegroundColor Yellow

try {
    # Keep the script running
    while ($true) {
        Start-Sleep -Seconds 1

        # Check if embedding service is still running
        if ($embeddingJob.State -ne "Running") {
            Write-Host "⚠ Embedding service stopped unexpectedly" -ForegroundColor Red
            break
        }
    }
} finally {
    Write-Host ""
    Write-Host "Stopping services..." -ForegroundColor Yellow
    Stop-Job $embeddingJob -ErrorAction SilentlyContinue
    Remove-Job $embeddingJob -ErrorAction SilentlyContinue
    Write-Host "✓ All services stopped" -ForegroundColor Green
}
