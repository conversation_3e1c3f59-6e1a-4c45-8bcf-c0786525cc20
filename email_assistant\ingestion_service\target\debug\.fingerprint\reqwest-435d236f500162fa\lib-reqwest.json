{"rustc": 1842507548689473721, "features": "[\"__rustls\", \"__rustls-ring\", \"__tls\", \"h2\", \"http2\", \"rustls-tls\", \"rustls-tls-webpki-roots\", \"rustls-tls-webpki-roots-no-provider\", \"stream\"]", "declared_features": "[\"__rustls\", \"__rustls-ring\", \"__tls\", \"blocking\", \"brotli\", \"charset\", \"cookies\", \"default\", \"default-tls\", \"deflate\", \"gzip\", \"h2\", \"hickory-dns\", \"http2\", \"http3\", \"json\", \"macos-system-configuration\", \"multipart\", \"native-tls\", \"native-tls-alpn\", \"native-tls-vendored\", \"rustls-tls\", \"rustls-tls-manual-roots\", \"rustls-tls-manual-roots-no-provider\", \"rustls-tls-native-roots\", \"rustls-tls-native-roots-no-provider\", \"rustls-tls-no-provider\", \"rustls-tls-webpki-roots\", \"rustls-tls-webpki-roots-no-provider\", \"socks\", \"stream\", \"system-proxy\", \"trust-dns\", \"zstd\"]", "target": 8885864859914201979, "profile": 447021486742529345, "path": 12434675677363017947, "deps": [[40386456601120721, "percent_encoding", false, 15092931327459917350], [784494742817713399, "tower_service", false, 891412978182292050], [1288403060204016458, "tokio_util", false, 17582237371763785542], [1788832197870803419, "hyper_rustls", false, 4702290700510437724], [1906322745568073236, "pin_project_lite", false, 27178801118851068], [2054153378684941554, "tower_http", false, 7802459812160210257], [2517136641825875337, "sync_wrapper", false, 15079274337327401480], [2883436298747778685, "rustls_pki_types", false, 1035697240254743143], [3150220818285335163, "url", false, 12742737668362991113], [5695049318159433696, "tower", false, 8541137164939621937], [5907992341687085091, "webpki_roots", false, 10436398343254151568], [5986029879202738730, "log", false, 3818706470321733474], [7620660491849607393, "futures_core", false, 8584818371505192382], [9010263965687315507, "http", false, 4349961928876268954], [9689903380558560274, "serde", false, 694819115080761911], [10629569228670356391, "futures_util", false, 1407185829515809506], [11895591994124935963, "tokio_rustls", false, 453299351287021380], [11957360342995674422, "hyper", false, 8140407445045656268], [12393800526703971956, "tokio", false, 6137508459591548543], [13077212702700853852, "base64", false, 17858158205471067771], [14084095096285906100, "http_body", false, 14653673679686981800], [14359893265615549706, "h2", false, 17816222823321553908], [16066129441945555748, "bytes", false, 14757824830115038577], [16400140949089969347, "rustls", false, 11496959222346507727], [16542808166767769916, "serde_urlencoded", false, 6615452075231751270], [16680807377217054954, "hyper_util", false, 2249913128436515471], [16900715236047033623, "http_body_util", false, 10290862317673768159]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\reqwest-435d236f500162fa\\dep-lib-reqwest", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}