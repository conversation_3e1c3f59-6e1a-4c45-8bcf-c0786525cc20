# AI-Assisted Email Response System - Development Blueprint and LLM Prompts

This document outlines a detailed, step-by-step blueprint for building the AI-Assisted Email Response System, broken down into small, iterative chunks. Each step is designed to be incremental, building upon previous work and ensuring a cohesive and functional system. Following the blueprint, a series of prompts for a code-generation LLM are provided for each step, ensuring best practices and wiring together all components.

## Blueprint

### Phase 1: Core Infrastructure and Project Setup

**Goal:** Establish the foundational project structure, database, and containerization for development.

**Chunk 1.1: Project Initialization and Directory Structure**
*   **Step 1.1.1:** Create the root project directory `email_assistant`.
*   **Step 1.1.2:** Initialize a Rust project named `ingestion_service` within `email_assistant`.
*   **Step 1.1.3:** Initialize a Python project named `rag_service` within `email_assistant`.
*   **Step 1.1.4:** Create a `docker-compose.yml` file in the root directory to manage services.

**Chunk 1.2: PostgreSQL with pgvector Setup**
*   **Step 1.2.1:** Add PostgreSQL service to `docker-compose.yml`.
*   **Step 1.2.2:** Configure a Docker volume for PostgreSQL data persistence.
*   **Step 1.2.3:** Create a custom Dockerfile for PostgreSQL to enable the `pgvector` extension.
*   **Step 1.2.4:** Verify PostgreSQL and `pgvector` are running and accessible.

### Phase 2: Email Ingestion and Parsing (Rust Core)

**Goal:** Develop robust Rust modules for parsing emails from various formats and basic cleaning.

**Chunk 2.1: Basic .eml Parsing**
*   **Step 2.1.1:** In the `ingestion_service` Rust project, add `mail-parser` crate dependency.
*   **Step 2.1.2:** Create a Rust function `parse_eml` to parse a single `.eml` file into a structured Email object (e.g., a struct containing sender, recipient, subject, body, etc.).
*   **Step 2.1.3:** Implement basic error handling for malformed `.eml` files within `parse_eml`.
*   **Step 2.1.4:** Write unit tests for `parse_eml` with valid and invalid inputs.

**Chunk 2.2: Mbox Parsing**
*   **Step 2.2.1:** In the `ingestion_service` Rust project, add `mbox` crate dependency.
*   **Step 2.2.2:** Create a Rust function `parse_mbox` to iterate and parse emails from an `.mbox` file, leveraging `parse_eml` for individual email processing.
*   **Step 2.2.3:** Implement error handling for `.mbox` file reading and individual email parsing failures within `parse_mbox`.
*   **Step 2.2.4:** Write unit tests for `parse_mbox`.

**Chunk 2.3: Email Cleaning and Normalization**
*   **Step 2.3.1:** Implement a Rust function `html_to_plain_text` to convert HTML body to plain text, preserving basic formatting (lists, tables). This function should take an HTML string and return a plain text string.
*   **Step 2.3.2:** Create a placeholder Rust function `strip_signatures_and_quotes` that takes an email body and returns a cleaned version. This will be integrated with a Python microservice later if `mailgun/talon` is used.
*   **Step 2.3.3:** Modify the `Email` struct and parsing functions to apply `html_to_plain_text` and `strip_signatures_and_quotes` to the email body, storing the cleaned plain text.
*   **Step 2.3.4:** Write unit tests for `html_to_plain_text` and `strip_signatures_and_quotes`.

### Phase 3: Data Storage and Indexing

**Goal:** Persist parsed email metadata and embeddings into PostgreSQL.

**Chunk 3.1: PostgreSQL Schema Definition and Rust DB Setup**
*   **Step 3.1.1:** Create a `migrations` directory within `ingestion_service` and add a SQL migration file to define the `messages` table. This table should include columns: `id` (UUID, PK), `thread_id` (UUID, nullable), `subject` (TEXT), `sender` (TEXT), `recipients` (TEXT[]), `sent_date` (TIMESTAMP), `plain_text_content` (TEXT), `raw_eml_path` (TEXT, nullable), `embedding` (VECTOR(768), nullable).
*   **Step 3.1.2:** Add a SQL statement to enable the `pgvector` extension in the same migration file.
*   **Step 3.1.3:** In the `ingestion_service` Rust project, add `sqlx` crate dependency (with `postgres` and `uuid` features).
*   **Step 3.1.4:** Implement a Rust struct `Message` that maps to the `messages` table schema.
*   **Step 3.1.5:** Create a Rust function `establish_connection` to connect to the PostgreSQL database.
*   **Step 3.1.6:** Implement a Rust function `run_migrations` to apply pending SQL migrations using `sqlx-cli` or similar.

**Chunk 3.2: Data Ingestion into Database**
*   **Step 3.2.1:** In the `ingestion_service` Rust project, create a Rust function `insert_message` that takes a `&Message` struct and inserts it into the `messages` table, handling the `embedding` as a `pgvector` type.
*   **Step 3.2.2:** Modify the `parse_eml` and `parse_mbox` functions to return a `Message` struct.
*   **Step 3.2.3:** Create a Rust main function or an ingestion script that reads emails from a specified `.mbox` or `.eml` file, parses and cleans them, and then calls `insert_message` for each email.
*   **Step 3.2.4:** Write integration tests for the full ingestion pipeline, verifying data is correctly stored in PostgreSQL.

### Phase 4: Embedding Generation

**Goal:** Generate vector embeddings for email content.

**Chunk 4.1: Local Embedding Model Integration (Python)**
*   **Step 4.1.1:** In the `rag_service` Python project, install `sentence-transformers` and `fastapi`.
*   **Step 4.1.2:** Create a Python script `embedding_service.py` with a FastAPI application.
*   **Step 4.1.3:** In `embedding_service.py`, load the `multilingual-e5-large` model from `sentence-transformers` once on startup.
*   **Step 4.1.4:** Create a FastAPI endpoint `/embed` that accepts a JSON payload with a `text` field, generates an embedding for the text, and returns the embedding as a JSON array.
*   **Step 4.1.5:** Write unit tests for the `/embed` endpoint and embedding generation logic.

**Chunk 4.2: Embedding Workflow Integration**
*   **Step 4.2.1:** In the `ingestion_service` Rust project, add `reqwest` and `tokio` (with `full` feature) dependencies for making HTTP requests.
*   **Step 4.2.2:** Create an asynchronous Rust function `get_embedding` that sends the cleaned email text to the Python `embedding_service`'s `/embed` endpoint and parses the returned embedding.
*   **Step 4.2.3:** Modify the ingestion script (from Step 3.2.3) to call `get_embedding` for each parsed email *before* calling `insert_message`, and include the obtained embedding in the `Message` struct.
*   **Step 4.2.4:** Implement batching in the `get_embedding` function or the ingestion script to send multiple texts to the Python service in a single request for efficiency (if the Python service supports it, otherwise iterate). For now, start with individual calls.
*   **Step 4.2.5:** Update the `docker-compose.yml` to include the `rag_service` and ensure the `ingestion_service` can communicate with it.

### Phase 5: Desktop Application (UI)

**Goal:** Develop a basic standalone desktop application for user interaction.

**Chunk 5.1: Basic Application Structure**
*   **Step 5.1.1:** Initialize a Tauri project named `desktop_app` in the root `email_assistant` directory.
*   **Step 5.1.2:** Create a basic Tauri window with a title "AI Email Assistant".

**Chunk 5.2: Email Import Functionality**
*   **Step 5.2.1:** In the `desktop_app`'s frontend (e.g., `src/main.js` or `src/App.svelte`), implement a file input element that allows selecting `.mbox` or `.eml` files.
*   **Step 5.2.2:** Implement drag-and-drop functionality for email files onto the application window.
*   **Step 5.2.3:** Create a Tauri command (Rust function exposed to frontend) in `desktop_app`'s `src-tauri/src/main.rs` that receives the file path(s) of the selected/dropped emails.
*   **Step 5.2.4:** This Tauri command should then trigger the ingestion process in the `ingestion_service` (e.g., by making an HTTP call to a new endpoint in `ingestion_service` that accepts file paths).

**Chunk 5.3: Draft Display and Interaction**
*   **Step 5.3.1:** In the `desktop_app`'s frontend, create a UI section to display a list of processed emails (e.g., subject and sender).
*   **Step 5.3.2:** Implement a UI element (e.g., a button or context menu) to "Generate Draft" for a selected email from the displayed list. This action will send the selected email's content (or ID) to the `rag_service`.
*   **Step 5.3.3:** Create a display area (e.g., a multi-line text area) for the generated draft text.
*   **Step 5.3.4:** Add a "Copy to Clipboard" button next to the draft display area.

### Phase 6: RAG Pipeline and LLM Integration

**Goal:** Implement the core RAG logic for retrieving relevant emails and generating drafts using an LLM.

**Chunk 6.1: Vector Search Implementation (Rust Backend for Python Service)**
*   **Step 6.1.1:** In the `ingestion_service` Rust project, create a new asynchronous Rust function `search_similar_emails` that takes an embedding (vector) as input, performs a cosine similarity search against the `messages.embedding` column in PostgreSQL, and returns a list of top-k relevant `Message` structs (or their plain text content and metadata).
*   **Step 6.1.2:** Expose `search_similar_emails` via a new HTTP endpoint in the `ingestion_service` (e.g., `/search_emails`), accessible by the `rag_service`.

**Chunk 6.2: Prompt Composition and LLM Integration (Python)**
*   **Step 6.2.1:** In the `rag_service` Python project, install `langchain` (or `llama-index`) and `python-dotenv`.
*   **Step 6.2.2:** In `rag_service`, create a new FastAPI endpoint `/generate_draft` that accepts a JSON payload with the `incoming_email_text`.
*   **Step 6.2.3:** Inside `/generate_draft`:
    *   Call the local `embedding_service` (from Chunk 4.1) to get the embedding of the `incoming_email_text`.
    *   Make an HTTP request to the `ingestion_service`'s `/search_emails` endpoint (from Step 6.1.2) with the generated embedding to retrieve top-k relevant source emails.
    *   Use `langchain.prompts.PromptTemplate` to construct a RAG prompt. The prompt should include the `incoming_email_text`, the retrieved source email content, and instructions for generating a legal advisor's reply.
    *   Integrate with a local LLM (e.g., Llama-3 8B). For initial setup, assume an Ollama endpoint is available at `http://localhost:11434`. Use `langchain_community.llms.Ollama` to connect to this LLM.
    *   Call the LLM with the composed prompt to generate the draft reply.
    *   Return the generated draft and the source documents (or their relevant metadata for citations) as a JSON response.

**Chunk 6.3: Wiring RAG to Desktop App**
*   **Step 6.3.1:** In the `desktop_app`'s frontend (from Step 5.3.2), modify the "Generate Draft" action to send the selected email's `plain_text_content` to the `rag_service`'s `/generate_draft` endpoint.
*   **Step 6.3.2:** Upon receiving the response from `/generate_draft`, update the draft display area in the UI with the generated draft and any citations.

### Phase 7: Compliance and Governance Features

**Goal:** Begin implementing features to address GDPR and EU AI Act compliance.

**Chunk 7.1: Output Logging**
*   **Step 7.1.1:** In the `rag_service` Python project, implement a logging mechanism (e.g., Python's `logging` module) to log all requests to `/generate_draft` and their corresponding responses (incoming email text, retrieved source texts, prompt, generated draft, citations).
*   **Step 7.1.2:** Ensure logs are written to a persistent file (e.g., mounted Docker volume) and include timestamps and a unique transaction ID.

**Chunk 7.2: Pseudonymisation Placeholder**
*   **Step 7.2.1:** In the `rag_service` Python project, create a new Python module `pseudonymizer.py` with a placeholder function `pseudonymize_text(text: str) -> str`. This function will initially just return the input text.
*   **Step 7.2.2:** Modify the `/generate_draft` endpoint in `rag_service` to call `pseudonymizer.pseudonymize_text` on the `incoming_email_text` and the `retrieved_source_texts` *before* they are sent to the LLM (if a cloud LLM is ever used). This sets up the architecture for future GDPR compliance.

### Phase 8: Deployment and Testing

**Goal:** Finalize containerization, implement comprehensive testing, and prepare for deployment.

**Chunk 8.1: Dockerization and Orchestration Refinement**
*   **Step 8.1.1:** Create a `Dockerfile` for the `ingestion_service` Rust application.
*   **Step 8.1.2:** Create a `Dockerfile` for the `rag_service` Python application.
*   **Step 8.1.3:** Update `docker-compose.yml` to define services for `ingestion_service` and `rag_service` using their respective Dockerfiles, ensuring proper network configuration and dependencies (e.g., `rag_service` depends on `ingestion_service` and `db`).
*   **Step 8.1.4:** Verify that all services can communicate and function correctly when orchestrated by `docker-compose up`.

**Chunk 8.2: Comprehensive Testing**
*   **Step 8.2.1:** Review and expand unit tests for all Rust and Python modules, aiming for high code coverage.
*   **Step 8.2.2:** Implement integration tests using `pytest` (for Python) and Rust's testing framework (`cargo test`) that simulate the flow of data between `ingestion_service`, `rag_service`, and PostgreSQL.
*   **Step 8.2.3:** Develop end-to-end tests for the `desktop_app` using a framework like Playwright or Cypress (if web-based UI) or a Tauri-specific testing tool, simulating user interactions from email import to draft generation.

**Chunk 8.3: Performance and Security Baselines**
*   **Step 8.3.1:** Conduct basic performance benchmarks for email ingestion (e.g., time to process 1000 emails) and draft generation (e.g., latency for a single request).
*   **Step 8.3.2:** Perform a preliminary security review, checking for common vulnerabilities (e.g., API key exposure, SQL injection risks) in both Rust and Python code.

---

## LLM Prompts for Code Generation

Each prompt is designed to generate a specific, incremental piece of code.

### Phase 1 Prompts

```text
Prompt:
You are an expert Rust and Python developer.
Create the initial project structure for an AI email assistant.
1. Create a root directory named `email_assistant`.
2. Inside `email_assistant`, create a new Rust binary project named `ingestion_service` using `cargo new ingestion_service`.
3. Inside `email_assistant`, create a new Python virtual environment and project directory named `rag_service`. Include a `requirements.txt` file and an empty `main.py` in `rag_service`.
4. In the root `email_assistant` directory, create a `docker-compose.yml` file. This file should define a PostgreSQL service named `db` with a persistent volume, and a custom Dockerfile for `db` to enable the `pgvector` extension.
```

```text
Prompt:
You are an expert DevOps engineer.
Complete the `docker-compose.yml` and the PostgreSQL Dockerfile for the `email_assistant` project.

**`email_assistant/docker-compose.yml`:**
```yaml
version: '3.8'

services:
  db:
    build:
      context: ./db
      dockerfile: Dockerfile
    environment:
      POSTGRES_DB: email_db
      POSTGRES_USER: user
      POSTGRES_PASSWORD: password
    ports:
      - "5432:5432"
    volumes:
      - db_data:/var/lib/postgresql/data

volumes:
  db_data:
```

**`email_assistant/db/Dockerfile`:**
```dockerfile
FROM postgres:16-alpine

RUN apk add --no-cache build-base postgresql-dev \
    && git clone --branch v1.1.1 https://github.com/pgvector/pgvector.git \
    && cd pgvector \
    && make \
    && make install

COPY ./init.sql /docker-entrypoint-initdb.d/
```

**`email_assistant/db/init.sql`:**
```sql
CREATE EXTENSION IF NOT EXISTS vector;
```

Ensure the `db` directory and `init.sql` are created alongside the Dockerfile.
```

### Phase 2 Prompts

```text
Prompt:
You are an expert Rust developer.
Implement basic `.eml` parsing in the `ingestion_service` project.

**`email_assistant/ingestion_service/Cargo.toml`:**
Add `mail-parser = "0.7"` to dependencies.

**`email_assistant/ingestion_service/src/main.rs`:**
```rust
use mail_parser::{Message, MimeHeaders};
use std::fs;
use std::io;

// Define a simple struct to represent our parsed email for now
#[derive(Debug, PartialEq)]
pub struct ParsedEmail {
    pub subject: Option<String>,
    pub from: Option<String>,
    pub to: Vec<String>,
    pub plain_text_body: Option<String>,
    pub html_body: Option<String>,
}

/// Parses a single .eml file into a ParsedEmail struct.
/// Handles basic error cases for file reading and parsing.
pub fn parse_eml(file_path: &str) -> Result<ParsedEmail, io::Error> {
    let content = fs::read(file_path)?;
    let message = Message::parse(&content)
        .ok_or_else(|| io::Error::new(io::ErrorKind::InvalidData, "Failed to parse email message"))?;

    let mut to_recipients = Vec::new();
    if let Some(to_addrs) = message.to() {
        for addr in to_addrs.iter() {
            if let Some(address) = addr.address {
                to_recipients.push(address.to_string());
            }
        }
    }

    Ok(ParsedEmail {
        subject: message.subject().map(|s| s.to_string()),
        from: message.from().and_then(|f| f.first().and_then(|addr| addr.address.map(|s| s.to_string()))),
        to: to_recipients,
        plain_text_body: message.get_body().and_then(|body| body.as_text().map(|s| s.to_string())),
        html_body: message.get_body().and_then(|body| body.as_html().map(|s| s.to_string())),
    })
}

fn main() {
    // Example usage:
    // let result = parse_eml("path/to/your/email.eml");
    // match result {
    //     Ok(email) => println!("Parsed Email: {:?}", email),
    //     Err(e) => eprintln!("Error parsing email: {}", e),
    // }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::fs;
    use std::io::Write;
    use tempfile::NamedTempFile;

    #[test]
    fn test_parse_valid_eml() -> Result<(), Box<dyn std::error::Error>> {
        let eml_content = b"From: <EMAIL>\r\nSubject: Test Subject\r\nTo: <EMAIL>\r\n\r\nHello, this is a test email.";
        let mut file = NamedTempFile::new()?;
        file.write_all(eml_content)?;
        let file_path = file.path().to_str().unwrap();

        let parsed_email = parse_eml(file_path)?;
        assert_eq!(parsed_email.subject, Some("Test Subject".to_string()));
        assert_eq!(parsed_email.from, Some("<EMAIL>".to_string()));
        assert_eq!(parsed_email.to, vec!["<EMAIL>".to_string()]);
        assert_eq!(parsed_email.plain_text_body, Some("Hello, this is a test email.".to_string()));
        assert_eq!(parsed_email.html_body, None);
        Ok(())
    }

    #[test]
    fn test_parse_eml_with_html_body() -> Result<(), Box<dyn std::error::Error>> {
        let eml_content = b"From: <EMAIL>\r\nSubject: HTML Test\r\nTo: <EMAIL>\r\nContent-Type: text/html\r\n\r\n<html><body><h1>Hello</h1><p>This is <b>HTML</b>.</p></body></html>";
        let mut file = NamedTempFile::new()?;
        file.write_all(eml_content)?;
        let file_path = file.path().to_str().unwrap();

        let parsed_email = parse_eml(file_path)?;
        assert_eq!(parsed_email.subject, Some("HTML Test".to_string()));
        assert_eq!(parsed_email.plain_text_body, None); // mail-parser might not extract plain from HTML by default
        assert_eq!(parsed_email.html_body, Some("<html><body><h1>Hello</h1><p>This is <b>HTML</b>.</p></body></html>".to_string()));
        Ok(())
    }

    #[test]
    fn test_parse_invalid_eml() -> Result<(), Box<dyn std::error::Error>> {
        let eml_content = b"This is not a valid email format.";
        let mut file = NamedTempFile::new()?;
        file.write_all(eml_content)?;
        let file_path = file.path().to_str().unwrap();

        let result = parse_eml(file_path);
        assert!(result.is_err());
        let err = result.unwrap_err();
        assert_eq!(err.kind(), io::ErrorKind::InvalidData);
        Ok(())
    }

    #[test]
    fn test_parse_non_existent_eml() {
        let result = parse_eml("non_existent_file.eml");
        assert!(result.is_err());
        let err = result.unwrap_err();
        assert_eq!(err.kind(), io::ErrorKind::NotFound);
    }
}
```
```

```text
Prompt:
You are an expert Rust developer.
Implement `.mbox` parsing in the `ingestion_service` project, leveraging the existing `parse_eml` function.

**`email_assistant/ingestion_service/Cargo.toml`:**
Add `mbox = "0.7"` to dependencies.

**`email_assistant/ingestion_service/src/main.rs`:**
Modify `main.rs` to include the `parse_mbox` function and update the main function with an example of its usage.

```rust
use mail_parser::{Message, MimeHeaders};
use std::fs;
use std::io::{self, BufReader, Read};
use std::path::Path;

// Re-use ParsedEmail struct from previous step
#[derive(Debug, PartialEq)]
pub struct ParsedEmail {
    pub subject: Option<String>,
    pub from: Option<String>,
    pub to: Vec<String>,
    pub plain_text_body: Option<String>,
    pub html_body: Option<String>,
}

/// Parses a single .eml file into a ParsedEmail struct.
/// (Keep this function as is from the previous step)
pub fn parse_eml(file_path: &str) -> Result<ParsedEmail, io::Error> {
    let content = fs::read(file_path)?;
    let message = Message::parse(&content)
        .ok_or_else(|| io::Error::new(io::ErrorKind::InvalidData, "Failed to parse email message"))?;

    let mut to_recipients = Vec::new();
    if let Some(to_addrs) = message.to() {
        for addr in to_addrs.iter() {
            if let Some(address) = addr.address {
                to_recipients.push(address.to_string());
            }
        }
    }

    Ok(ParsedEmail {
        subject: message.subject().map(|s| s.to_string()),
        from: message.from().and_then(|f| f.first().and_then(|addr| addr.address.map(|s| s.to_string()))),
        to: to_recipients,
        plain_text_body: message.get_body().and_then(|body| body.as_text().map(|s| s.to_string())),
        html_body: message.get_body().and_then(|body| body.as_html().map(|s| s.to_string())),
    })
}

/// Parses an .mbox file, returning a vector of ParsedEmail structs.
/// Handles errors for individual email parsing by logging and skipping.
pub fn parse_mbox(file_path: &str) -> Result<Vec<ParsedEmail>, io::Error> {
    let file = fs::File::open(file_path)?;
    let reader = BufReader::new(file);
    let mut emails = Vec::new();

    for result in mbox::IntoMessages::new(reader) {
        match result {
            Ok(message_bytes) => {
                let message = Message::parse(&message_bytes)
                    .ok_or_else(|| io::Error::new(io::ErrorKind::InvalidData, "Failed to parse email message from mbox chunk"))?;

                let mut to_recipients = Vec::new();
                if let Some(to_addrs) = message.to() {
                    for addr in to_addrs.iter() {
                        if let Some(address) = addr.address {
                            to_recipients.push(address.to_string());
                        }
                    }
                }

                emails.push(ParsedEmail {
                    subject: message.subject().map(|s| s.to_string()),
                    from: message.from().and_then(|f| f.first().and_then(|addr| addr.address.map(|s| s.to_string()))),
                    to: to_recipients,
                    plain_text_body: message.get_body().and_then(|body| body.as_text().map(|s| s.to_string())),
                    html_body: message.get_body().and_then(|body| body.as_html().map(|s| s.to_string())),
                });
            },
            Err(e) => {
                eprintln!("Error reading mbox message: {}", e);
                // Continue processing other messages
            }
        }
    }
    Ok(emails)
}

fn main() {
    // Example usage for .mbox parsing
    // To run this, you'll need a sample .mbox file.
    // For example, create a file named 'sample.mbox' in the ingestion_service directory
    // with content like:
    // From MAILER-DAEMON Mon Apr 18 11:50:00 2000
    // From: <EMAIL>
    // Subject: Test Email 1
    // To: <EMAIL>
    //
    // This is the body of the first email.
    // From MAILER-DAEMON Mon Apr 18 11:51:00 2000
    // From: <EMAIL>
    // Subject: Test Email 2
    // To: <EMAIL>
    //
    // This is the body of the second email.

    let mbox_path = "sample.mbox"; // Replace with your mbox file path
    match parse_mbox(mbox_path) {
        Ok(emails) => {
            for email in emails {
                println!("Parsed Mbox Email: {:?}", email);
            }
        },
        Err(e) => eprintln!("Error parsing mbox file: {}", e),
    }

    // Example usage for .eml parsing (from previous step)
    // let eml_path = "sample.eml"; // Replace with your eml file path
    // match parse_eml(eml_path) {
    //     Ok(email) => println!("Parsed EML Email: {:?}", email),
    //     Err(e) => eprintln!("Error parsing eml file: {}", e),
    // }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::fs::File;
    use std::io::Write;
    use tempfile::NamedTempFile;

    // ... (keep test_parse_valid_eml, test_parse_eml_with_html_body, test_parse_invalid_eml, test_parse_non_existent_eml from previous step)

    #[test]
    fn test_parse_valid_mbox() -> Result<(), Box<dyn std::error::Error>> {
        let mbox_content = b"From MAILER-DAEMON Mon Apr 18 11:50:00 2000\n\
                              From: <EMAIL>\n\
                              Subject: Test Mbox Subject 1\n\
                              To: <EMAIL>\n\n\
                              Body of email 1.\n\
                              From MAILER-DAEMON Mon Apr 18 11:51:00 2000\n\
                              From: <EMAIL>\n\
                              Subject: Test Mbox Subject 2\n\
                              To: <EMAIL>\n\n\
                              Body of email 2.";

        let mut file = NamedTempFile::new()?;
        file.write_all(mbox_content)?;
        let file_path = file.path().to_str().unwrap();

        let parsed_emails = parse_mbox(file_path)?;
        assert_eq!(parsed_emails.len(), 2);

        assert_eq!(parsed_emails[0].subject, Some("Test Mbox Subject 1".to_string()));
        assert_eq!(parsed_emails[0].from, Some("<EMAIL>".to_string()));
        assert_eq!(parsed_emails[0].to, vec!["<EMAIL>".to_string()]);
        assert_eq!(parsed_emails[0].plain_text_body, Some("Body of email 1.".to_string()));

        assert_eq!(parsed_emails[1].subject, Some("Test Mbox Subject 2".to_string()));
        assert_eq!(parsed_emails[1].from, Some("<EMAIL>".to_string()));
        assert_eq!(parsed_emails[1].to, vec!["<EMAIL>".to_string()]);
        assert_eq!(parsed_emails[1].plain_text_body, Some("Body of email 2.".to_string()));
        Ok(())
    }

    #[test]
    fn test_parse_empty_mbox() -> Result<(), Box<dyn std::error::Error>> {
        let mbox_content = b"";
        let mut file = NamedTempFile::new()?;
        file.write_all(mbox_content)?;
        let file_path = file.path().to_str().unwrap();

        let parsed_emails = parse_mbox(file_path)?;
        assert!(parsed_emails.is_empty());
        Ok(())
    }

    #[test]
    fn test_parse_mbox_with_malformed_email() -> Result<(), Box<dyn std::error::Error>> {
        let mbox_content = b"From MAILER-DAEMON Mon Apr 18 11:50:00 2000\n\
                              From: <EMAIL>\n\
                              Subject: Good Email\n\
                              To: <EMAIL>\n\n\
                              This is a good email.\n\
                              From MAILER-DAEMON Mon Apr 18 11:51:00 2000\n\
                              This is not a valid email format.\n\
                              From MAILER-DAEMON Mon Apr 18 11:52:00 2000\n\
                              From: <EMAIL>\n\
                              Subject: Another Good Email\n\
                              To: <EMAIL>\n\n\
                              This is another good email.";

        let mut file = NamedTempFile::new()?;
        file.write_all(mbox_content)?;
        let file_path = file.path().to_str().unwrap();

        let parsed_emails = parse_mbox(file_path)?;
        assert_eq!(parsed_emails.len(), 2); // Should skip the malformed one

        assert_eq!(parsed_emails[0].subject, Some("Good Email".to_string()));
        assert_eq!(parsed_emails[1].subject, Some("Another Good Email".to_string()));
        Ok(())
    }
}
```
```

```text
Prompt:
You are an expert Rust developer.
Implement email cleaning and normalization functions in `ingestion_service`.

**`email_assistant/ingestion_service/Cargo.toml`:**
Add `html2text = "0.5"` to dependencies.

**`email_assistant/ingestion_service/src/main.rs`:**
Modify `main.rs` to include `html_to_plain_text` and `strip_signatures_and_quotes`. Update `ParsedEmail` struct and parsing functions to apply these.

```rust
use mail_parser::{Message, MimeHeaders};
use std::fs;
use std::io::{self, BufReader, Read};
use std::path::Path;

// Update ParsedEmail to store cleaned plain text
#[derive(Debug, PartialEq)]
pub struct ParsedEmail {
    pub subject: Option<String>,
    pub from: Option<String>,
    pub to: Vec<String>,
    pub plain_text_body_raw: Option<String>, // Original plain text body
    pub html_body_raw: Option<String>,       // Original HTML body
    pub cleaned_plain_text_body: Option<String>, // New field for cleaned text
}

/// Converts an HTML string to plain text, preserving basic formatting.
pub fn html_to_plain_text(html: &str) -> String {
    let mut converter = html2text::from_read(html.as_bytes(), 80);
    converter.set_render_links(false); // Do not render links as [URL]
    converter.set_wrap_links(false); // Do not wrap links
    converter.set_skip_images(true); // Skip image alt text
    converter.from_html()
}

/// Placeholder for stripping signatures and quotes.
/// In a real scenario, this might involve more complex logic or external services.
pub fn strip_signatures_and_quotes(text: &str) -> String {
    // Simple heuristic: remove lines starting with "-- " (common signature delimiter)
    // and lines that look like quoted replies (e.g., "> On Date, Sender wrote:")
    let mut cleaned_lines = Vec::new();
    let mut in_signature = false;
    for line in text.lines() {
        if line.trim().starts_with("-- ") && line.trim().len() < 50 { // Basic check for signature line
            in_signature = true;
            continue;
        }
        if in_signature {
            continue; // Skip lines after a potential signature start
        }

        if line.starts_with('>') {
            // Simple check for quoted lines. More robust would check full "On Date, Sender wrote:" patterns.
            continue;
        }

        cleaned_lines.push(line);
    }
    cleaned_lines.join("\n")
}


/// Parses a single .eml file into a ParsedEmail struct.
/// Applies cleaning functions.
pub fn parse_eml(file_path: &str) -> Result<ParsedEmail, io::Error> {
    let content = fs::read(file_path)?;
    let message = Message::parse(&content)
        .ok_or_else(|| io::Error::new(io::ErrorKind::InvalidData, "Failed to parse email message"))?;

    let mut to_recipients = Vec::new();
    if let Some(to_addrs) = message.to() {
        for addr in to_addrs.iter() {
            if let Some(address) = addr.address {
                to_recipients.push(address.to_string());
            }
        }
    }

    let plain_text_body_raw = message.get_body().and_then(|body| body.as_text().map(|s| s.to_string()));
    let html_body_raw = message.get_body().and_then(|body| body.as_html().map(|s| s.to_string()));

    let cleaned_plain_text_body = if let Some(html) = &html_body_raw {
        let plain = html_to_plain_text(html);
        Some(strip_signatures_and_quotes(&plain))
    } else if let Some(plain) = &plain_text_body_raw {
        Some(strip_signatures_and_quotes(plain))
    } else {
        None
    };

    Ok(ParsedEmail {
        subject: message.subject().map(|s| s.to_string()),
        from: message.from().and_then(|f| f.first().and_then(|addr| addr.address.map(|s| s.to_string()))),
        to: to_recipients,
        plain_text_body_raw,
        html_body_raw,
        cleaned_plain_text_body,
    })
}

/// Parses an .mbox file, returning a vector of ParsedEmail structs.
/// Applies cleaning functions.
pub fn parse_mbox(file_path: &str) -> Result<Vec<ParsedEmail>, io::Error> {
    let file = fs::File::open(file_path)?;
    let reader = BufReader::new(file);
    let mut emails = Vec::new();

    for result in mbox::IntoMessages::new(reader) {
        match result {
            Ok(message_bytes) => {
                let message = Message::parse(&message_bytes)
                    .ok_or_else(|| io::Error::new(io::ErrorKind::InvalidData, "Failed to parse email message from mbox chunk"))?;

                let mut to_recipients = Vec::new();
                if let Some(to_addrs) = message.to() {
                    for addr in to_addrs.iter() {
                        if let Some(address) = addr.address {
                            to_recipients.push(address.to_string());
                        }
                    }
                }

                let plain_text_body_raw = message.get_body().and_then(|body| body.as_text().map(|s| s.to_string()));
                let html_body_raw = message.get_body().and_then(|body| body.as_html().map(|s| s.to_string()));

                let cleaned_plain_text_body = if let Some(html) = &html_body_raw {
                    let plain = html_to_plain_text(html);
                    Some(strip_signatures_and_quotes(&plain))
                } else if let Some(plain) = &plain_text_body_raw {
                    Some(strip_signatures_and_quotes(plain))
                } else {
                    None
                };

                emails.push(ParsedEmail {
                    subject: message.subject().map(|s| s.to_string()),
                    from: message.from().and_then(|f| f.first().and_then(|addr| addr.address.map(|s| s.to_string()))),
                    to: to_recipients,
                    plain_text_body_raw,
                    html_body_raw,
                    cleaned_plain_text_body,
                });
            },
            Err(e) => {
                eprintln!("Error reading mbox message: {}", e);
            }
        }
    }
    Ok(emails)
}

fn main() {
    // Example usage for .mbox parsing
    // (Keep as is from previous step, but now it will use cleaned_plain_text_body)
    let mbox_path = "sample.mbox";
    match parse_mbox(mbox_path) {
        Ok(emails) => {
            for email in emails {
                println!("Parsed Mbox Email (Cleaned Body): {:?}", email.cleaned_plain_text_body);
            }
        },
        Err(e) => eprintln!("Error parsing mbox file: {}", e),
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::fs::File;
    use std::io::Write;
    use tempfile::NamedTempFile;

    // ... (keep previous eml and mbox parsing tests, adjusting assertions for new struct fields)

    #[test]
    fn test_html_to_plain_text() {
        let html_content = "<html><body><h1>Hello</h1><p>This is <b>HTML</b> with a <a href=\"http://example.com\">link</a>.</p><ul><li>Item 1</li><li>Item 2</li></ul></body></html>";
        let expected_plain = "Hello\n\nThis is HTML with a link.\n\n* Item 1\n* Item 2";
        assert_eq!(html_to_plain_text(html_content).trim(), expected_plain.trim());
    }

    #[test]
    fn test_strip_signatures_and_quotes() {
        let text_with_signature = "Hello,\n\nThis is the main body.\n\n-- \nJohn Doe\nSoftware Engineer";
        let expected_cleaned = "Hello,\n\nThis is the main body.";
        assert_eq!(strip_signatures_and_quotes(text_with_signature), expected_cleaned);

        let text_with_quote = "Hello,\n\nMy reply.\n\n> On Mon, Jan 1, 2023 at 10:00 AM John Doe <<EMAIL>> wrote:\n> This is a quoted message.";
        let expected_cleaned_quote = "Hello,\n\nMy reply.";
        assert_eq!(strip_signatures_and_quotes(text_with_quote), expected_cleaned_quote);

        let mixed_content = "Main content here.\n> Quoted line 1\n> Quoted line 2\n-- \nSignature";
        let expected_mixed = "Main content here.";
        assert_eq!(strip_signatures_and_quotes(mixed_content), expected_mixed);

        let no_change_content = "Just plain text.";
        assert_eq!(strip_signatures_and_quotes(no_change_content), no_change_content);
    }

    #[test]
    fn test_parse_eml_with_cleaning() -> Result<(), Box<dyn std::error::Error>> {
        let eml_content = b"From: <EMAIL>\r\nSubject: Clean Test\r\nTo: <EMAIL>\r\nContent-Type: text/html\r\n\r\n<html><body><p>Test body.</p><p>-- </p><p>Signature</p></body></html>";
        let mut file = NamedTempFile::new()?;
        file.write_all(eml_content)?;
        let file_path = file.path().to_str().unwrap();

        let parsed_email = parse_eml(file_path)?;
        assert_eq!(parsed_email.cleaned_plain_text_body, Some("Test body.".to_string()));
        Ok(())
    }
}
```
```

### Phase 3 Prompts

```text
Prompt:
You are an expert Rust developer.
Set up PostgreSQL schema and Rust database connection in `ingestion_service`.

**`email_assistant/ingestion_service/Cargo.toml`:**
Add `sqlx = { version = "0.7", features = ["runtime-tokio-rustls", "postgres", "uuid", "macros", "chrono"] }` and `uuid = { version = "1.8", features = ["v4"] }` and `chrono = { version = "0.4", features = ["serde"] }` to dependencies.
Add `tokio = { version = "1.37", features = ["full"] }` to dependencies.

**`email_assistant/ingestion_service/src/main.rs`:**
Refactor `ParsedEmail` into a new `Message` struct that maps to the database schema. Implement `establish_connection` and `run_migrations`.

```rust
use mail_parser::{Message as MailParserMessage, MimeHeaders};
use std::fs;
use std::io::{self, BufReader, Read};
use std::path::Path;
use sqlx::{PgPool, Pool, Postgres, migrate::Migrator};
use uuid::Uuid;
use chrono::{DateTime, Utc};
use serde::{Serialize, Deserialize};

// --- Email Parsing and Cleaning Functions (from previous steps) ---
// Keep html_to_plain_text and strip_signatures_and_quotes as is
pub fn html_to_plain_text(html: &str) -> String {
    let mut converter = html2text::from_read(html.as_bytes(), 80);
    converter.set_render_links(false);
    converter.set_wrap_links(false);
    converter.set_skip_images(true);
    converter.from_html()
}

pub fn strip_signatures_and_quotes(text: &str) -> String {
    let mut cleaned_lines = Vec::new();
    let mut in_signature = false;
    for line in text.lines() {
        if line.trim().starts_with("-- ") && line.trim().len() < 50 {
            in_signature = true;
            continue;
        }
        if in_signature {
            continue;
        }
        if line.starts_with('>') {
            continue;
        }
        cleaned_lines.push(line);
    }
    cleaned_lines.join("\n")
}
// --- End Email Parsing and Cleaning Functions ---

// New Message struct for database mapping
#[derive(Debug, Clone, PartialEq, sqlx::FromRow, Serialize, Deserialize)]
pub struct Message {
    pub id: Uuid,
    pub thread_id: Option<Uuid>,
    pub subject: Option<String>,
    pub sender: Option<String>,
    pub recipients: Vec<String>, // Stored as TEXT[] in Postgres
    pub sent_date: Option<DateTime<Utc>>,
    pub plain_text_content: Option<String>,
    pub raw_eml_path: Option<String>,
    pub embedding: Option<Vec<f32>>, // Will be `vector` type in Postgres
}

/// Parses a single .eml file into a Message struct.
/// Applies cleaning functions.
pub fn parse_eml_to_message(file_path: &str) -> Result<Message, io::Error> {
    let content = fs::read(file_path)?;
    let mail_parser_message = MailParserMessage::parse(&content)
        .ok_or_else(|| io::Error::new(io::ErrorKind::InvalidData, "Failed to parse email message"))?;

    let mut recipients = Vec::new();
    if let Some(to_addrs) = mail_parser_message.to() {
        for addr in to_addrs.iter() {
            if let Some(address) = addr.address {
                recipients.push(address.to_string());
            }
        }
    }

    let plain_text_body_raw = mail_parser_message.get_body().and_then(|body| body.as_text().map(|s| s.to_string()));
    let html_body_raw = mail_parser_message.get_body().and_then(|body| body.as_html().map(|s| s.to_string()));

    let cleaned_plain_text_content = if let Some(html) = &html_body_raw {
        let plain = html_to_plain_text(html);
        Some(strip_signatures_and_quotes(&plain))
    } else if let Some(plain) = &plain_text_body_raw {
        Some(strip_signatures_and_quotes(plain))
    } else {
        None
    };

    let sent_date = mail_parser_message.date().map(|dt| DateTime::<Utc>::from_naive_utc_and_offset(dt.naive_utc(), Utc));

    Ok(Message {
        id: Uuid::new_v4(), // Generate a new UUID for each message
        thread_id: None, // Placeholder for future thread detection
        subject: mail_parser_message.subject().map(|s| s.to_string()),
        sender: mail_parser_message.from().and_then(|f| f.first().and_then(|addr| addr.address.map(|s| s.to_string()))),
        recipients,
        sent_date,
        plain_text_content: cleaned_plain_text_content,
        raw_eml_path: Some(file_path.to_string()), // Store path for reference
        embedding: None, // Will be filled in later
    })
}

/// Parses an .mbox file, returning a vector of Message structs.
/// Applies cleaning functions.
pub fn parse_mbox_to_messages(file_path: &str) -> Result<Vec<Message>, io::Error> {
    let file = fs::File::open(file_path)?;
    let reader = BufReader::new(file);
    let mut messages = Vec::new();

    for result in mbox::IntoMessages::new(reader) {
        match result {
            Ok(message_bytes) => {
                let mail_parser_message = MailParserMessage::parse(&message_bytes)
                    .ok_or_else(|| io::Error::new(io::ErrorKind::InvalidData, "Failed to parse email message from mbox chunk"))?;

                let mut recipients = Vec::new();
                if let Some(to_addrs) = mail_parser_message.to() {
                    for addr in to_addrs.iter() {
                        if let Some(address) = addr.address {
                            recipients.push(address.to_string());
                        }
                    }
                }

                let plain_text_body_raw = mail_parser_message.get_body().and_then(|body| body.as_text().map(|s| s.to_string()));
                let html_body_raw = mail_parser_message.get_body().and_then(|body| body.as_html().map(|s| s.to_string()));

                let cleaned_plain_text_content = if let Some(html) = &html_body_raw {
                    let plain = html_to_plain_text(html);
                    Some(strip_signatures_and_quotes(&plain))
                } else if let Some(plain) = &plain_text_body_raw {
                    Some(strip_signatures_and_quotes(plain))
                } else {
                    None
                };

                let sent_date = mail_parser_message.date().map(|dt| DateTime::<Utc>::from_naive_utc_and_offset(dt.naive_utc(), Utc));

                messages.push(Message {
                    id: Uuid::new_v4(),
                    thread_id: None,
                    subject: mail_parser_message.subject().map(|s| s.to_string()),
                    sender: mail_parser_message.from().and_then(|f| f.first().and_then(|addr| addr.address.map(|s| s.to_string()))),
                    recipients,
                    sent_date,
                    plain_text_content: cleaned_plain_text_content,
                    raw_eml_path: None, // No specific file path for mbox messages
                    embedding: None,
                });
            },
            Err(e) => {
                eprintln!("Error reading mbox message: {}", e);
            }
        }
    }
    Ok(messages)
}

// Database setup
pub async fn establish_connection() -> Result<PgPool, sqlx::Error> {
    let database_url = std::env::var("DATABASE_URL")
        .unwrap_or_else(|_| "postgres://user:password@localhost:5432/email_db".to_string());
    PgPool::connect(&database_url).await
}

// Migrations
static MIGRATOR: Migrator = Migrator::new(std::path::Path::new("./migrations"));

pub async fn run_migrations(pool: &PgPool) -> Result<(), sqlx::migrate::MigrateError> {
    MIGRATOR.run(pool).await
}

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    dotenv::dotenv().ok(); // Load .env file

    let pool = establish_connection().await?;
    println!("Database connection established.");

    run_migrations(&pool).await?;
    println!("Migrations executed.");

    // Example usage for .mbox parsing and printing messages
    let mbox_path = "sample.mbox