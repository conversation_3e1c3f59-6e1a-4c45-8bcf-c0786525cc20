{"rustc": 1842507548689473721, "features": "[\"_rt-tokio\", \"_tls-rustls\", \"any\", \"chrono\", \"crc\", \"default\", \"json\", \"migrate\", \"offline\", \"rustls\", \"rustls-pemfile\", \"serde\", \"serde_json\", \"sha2\", \"tokio\", \"tokio-stream\", \"uuid\", \"webpki-roots\"]", "declared_features": "[\"_rt-async-std\", \"_rt-tokio\", \"_tls-native-tls\", \"_tls-none\", \"_tls-rustls\", \"any\", \"async-io\", \"async-std\", \"bigdecimal\", \"bit-vec\", \"bstr\", \"chrono\", \"crc\", \"default\", \"digest\", \"encoding_rs\", \"ipnetwork\", \"json\", \"mac_address\", \"migrate\", \"native-tls\", \"num-bigint\", \"offline\", \"regex\", \"rust_decimal\", \"rustls\", \"rustls-pemfile\", \"serde\", \"serde_json\", \"sha1\", \"sha2\", \"time\", \"tokio\", \"tokio-stream\", \"uuid\", \"webpki-roots\"]", "target": 2042750936636613814, "profile": 2241668132362809309, "path": 11940386072098527829, "deps": [[5103565458935487, "futures_io", false, 6195738813273311032], [40386456601120721, "percent_encoding", false, 15092931327459917350], [530211389790465181, "hex", false, 13592508430756754095], [788558663644978524, "crossbeam_queue", false, 11963205886906350923], [966925859616469517, "ahash", false, 10116289632869587396], [1162433738665300155, "crc", false, 6965705147014766010], [1464803193346256239, "event_listener", false, 3585883930955679372], [1811549171721445101, "futures_channel", false, 12103155504450959211], [3150220818285335163, "url", false, 5995438434888171516], [3405817021026194662, "hashlink", false, 12555309046903465295], [3646857438214563691, "futures_intrusive", false, 9882763536466327170], [3666196340704888985, "smallvec", false, 3424155135914433710], [3712811570531045576, "byteorder", false, 13377788656444667563], [3722963349756955755, "once_cell", false, 13244492203321078007], [5986029879202738730, "log", false, 3818706470321733474], [6493259146304816786, "indexmap", false, 3169632620522197897], [7620660491849607393, "futures_core", false, 8584818371505192382], [8008191657135824715, "thiserror", false, 8493927907267554123], [8319709847752024821, "uuid", false, 1910560137687000915], [8606274917505247608, "tracing", false, 11736356935175720532], [9689903380558560274, "serde", false, 12682574395712889020], [9857275760291862238, "sha2", false, 14840502100087200569], [9897246384292347999, "chrono", false, 13055332157358921875], [10629569228670356391, "futures_util", false, 7453161946998814878], [10862088793507253106, "sqlformat", false, 10723471297390327953], [11295624341523567602, "rustls", false, 6640789404466757671], [12170264697963848012, "either", false, 3991707315559450922], [12393800526703971956, "tokio", false, 8400104909120744388], [15367738274754116744, "serde_json", false, 4963293940932014847], [15932120279885307830, "memchr", false, 3753662393507526803], [16066129441945555748, "bytes", false, 14757824830115038577], [16311359161338405624, "rustls_pemfile", false, 553439493652304894], [16973251432615581304, "tokio_stream", false, 15344424522579835764], [17106256174509013259, "atoi", false, 1786173579722790599], [17605717126308396068, "paste", false, 18420332913555425673], [17652733826348741533, "webpki_roots", false, 15712200293735448500]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\sqlx-core-342b75c8a016cc04\\dep-lib-sqlx_core", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}