{"rustc": 1842507548689473721, "features": "[\"_rt-tokio\", \"_tls-rustls\", \"any\", \"chrono\", \"crc\", \"default\", \"json\", \"migrate\", \"offline\", \"rustls\", \"rustls-pemfile\", \"serde\", \"serde_json\", \"sha2\", \"tokio\", \"tokio-stream\", \"uuid\", \"webpki-roots\"]", "declared_features": "[\"_rt-async-std\", \"_rt-tokio\", \"_tls-native-tls\", \"_tls-none\", \"_tls-rustls\", \"any\", \"async-io\", \"async-std\", \"bigdecimal\", \"bit-vec\", \"bstr\", \"chrono\", \"crc\", \"default\", \"digest\", \"encoding_rs\", \"ipnetwork\", \"json\", \"mac_address\", \"migrate\", \"native-tls\", \"num-bigint\", \"offline\", \"regex\", \"rust_decimal\", \"rustls\", \"rustls-pemfile\", \"serde\", \"serde_json\", \"sha1\", \"sha2\", \"time\", \"tokio\", \"tokio-stream\", \"uuid\", \"webpki-roots\"]", "target": 2042750936636613814, "profile": 2225463790103693989, "path": 11940386072098527829, "deps": [[5103565458935487, "futures_io", false, 2935228756808637854], [40386456601120721, "percent_encoding", false, 6863772115486772403], [530211389790465181, "hex", false, 15041834199505872645], [788558663644978524, "crossbeam_queue", false, 3348887000366150912], [966925859616469517, "ahash", false, 46100209202553591], [1162433738665300155, "crc", false, 17859096353872371111], [1464803193346256239, "event_listener", false, 10394173705278652196], [1811549171721445101, "futures_channel", false, 7402592693446283843], [3150220818285335163, "url", false, 3798716325632857585], [3405817021026194662, "hashlink", false, 402761583809916246], [3646857438214563691, "futures_intrusive", false, 13217130739988702029], [3666196340704888985, "smallvec", false, 10106983271726061198], [3712811570531045576, "byteorder", false, 11812631572996424896], [3722963349756955755, "once_cell", false, 15031127281168514016], [5986029879202738730, "log", false, 16544253528419911208], [6493259146304816786, "indexmap", false, 15629209348251735261], [7620660491849607393, "futures_core", false, 17241864372584895432], [8008191657135824715, "thiserror", false, 10608708110099228899], [8319709847752024821, "uuid", false, 14942403109587454880], [8606274917505247608, "tracing", false, 6532648845087525491], [9689903380558560274, "serde", false, 12658549810467043323], [9857275760291862238, "sha2", false, 17876214779718661753], [9897246384292347999, "chrono", false, 13785585779211293819], [10629569228670356391, "futures_util", false, 6704846912205889935], [10862088793507253106, "sqlformat", false, 15494454536039414247], [11295624341523567602, "rustls", false, 16022438274583846844], [12170264697963848012, "either", false, 917060758155416179], [12393800526703971956, "tokio", false, 12161377879074787945], [15367738274754116744, "serde_json", false, 16767705763674330651], [15932120279885307830, "memchr", false, 15902002545204423573], [16066129441945555748, "bytes", false, 5101690495670737311], [16311359161338405624, "rustls_pemfile", false, 14801884352156199345], [16973251432615581304, "tokio_stream", false, 13241824057530877861], [17106256174509013259, "atoi", false, 8679083134689835762], [17605717126308396068, "paste", false, 18420332913555425673], [17652733826348741533, "webpki_roots", false, 9232664899457519231]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\sqlx-core-a7c9efb4d936ed12\\dep-lib-sqlx_core", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}