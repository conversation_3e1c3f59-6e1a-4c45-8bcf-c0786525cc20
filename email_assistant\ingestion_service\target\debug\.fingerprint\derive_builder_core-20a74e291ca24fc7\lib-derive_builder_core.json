{"rustc": 1842507548689473721, "features": "[\"lib_has_std\"]", "declared_features": "[\"alloc\", \"clippy\", \"lib_has_std\"]", "target": 15805722739128704647, "profile": 2225463790103693989, "path": 11944414702791965715, "deps": [[496455418292392305, "darling", false, 12961380623333198026], [3060637413840920116, "proc_macro2", false, 9742792036988073286], [4974441333307933176, "syn", false, 12057608429065043436], [17990358020177143287, "quote", false, 675908390363228251]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\derive_builder_core-20a74e291ca24fc7\\dep-lib-derive_builder_core", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}