{"rustc": 1842507548689473721, "features": "[\"_rt-tokio\", \"_tls-rustls\", \"chrono\", \"default\", \"json\", \"migrate\", \"sqlite\", \"sqlx-sqlite\", \"tokio\", \"uuid\"]", "declared_features": "[\"_rt-async-std\", \"_rt-tokio\", \"_tls-native-tls\", \"_tls-rustls\", \"async-std\", \"bigdecimal\", \"bit-vec\", \"chrono\", \"default\", \"ipnetwork\", \"json\", \"mac_address\", \"migrate\", \"mysql\", \"postgres\", \"rust_decimal\", \"sqlite\", \"sqlx-mysql\", \"sqlx-postgres\", \"sqlx-sqlite\", \"time\", \"tokio\", \"uuid\"]", "target": 961973412475639632, "profile": 2225463790103693989, "path": 18389220411245665339, "deps": [[530211389790465181, "hex", false, 15041834199505872645], [996810380461694889, "sqlx_core", false, 15441323543634552525], [1441306149310335789, "tempfile", false, 10880255939259751308], [2713742371683562785, "syn", false, 8593241034591342210], [3060637413840920116, "proc_macro2", false, 9742792036988073286], [3150220818285335163, "url", false, 3798716325632857585], [3405707034081185165, "dotenvy", false, 12616721378704070379], [3722963349756955755, "once_cell", false, 15031127281168514016], [8045585743974080694, "heck", false, 17511591055440357725], [9689903380558560274, "serde", false, 12658549810467043323], [9857275760291862238, "sha2", false, 17876214779718661753], [11838249260056359578, "sqlx_sqlite", false, 13918989974444942890], [12170264697963848012, "either", false, 917060758155416179], [12393800526703971956, "tokio", false, 12161377879074787945], [15367738274754116744, "serde_json", false, 16767705763674330651], [17990358020177143287, "quote", false, 675908390363228251]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\sqlx-macros-core-1d57a4fee76e5fa3\\dep-lib-sqlx_macros_core", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}