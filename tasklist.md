# AI-Assisted Email Response System - Task List

## Project Overview
Building a Retrieval-Augmented Generation (RAG) system for legal advisors to draft email replies. The system ingests historical emails, processes them with AI, and generates contextually relevant draft responses while maintaining GDPR and EU AI Act compliance.

## Architecture Summary
- **Core Components**: <PERSON><PERSON> (ingestion/parsing), Python (RAG/AI), <PERSON><PERSON> (desktop app)
- **Database**: PostgreSQL 16 with pgvector extension (local installation)
- **Development Environment**: Local native processes with inter-process communication
- **Compliance**: GDPR pseudonymisation, EU AI Act limited-risk classification

## Task Breakdown

### Phase 1: Core Infrastructure and Project Setup
- [ ] **Task 1.1**: Project Initialization and Directory Structure
  - Create root `email_assistant` directory
  - Initialize Rust project `ingestion_service`
  - Initialize Python project `rag_service`
  - Create `scripts/` and `config/` directories

- [ ] **Task 1.2**: Local PostgreSQL with pgvector Setup
  - Install PostgreSQL 16 locally on development machine
  - Install and configure pgvector extension
  - Create setup scripts for database initialization
  - Verify PostgreSQL and pgvector functionality locally

### Phase 2: Email Ingestion and Parsing (Rust Core)
- [ ] **Task 2.1**: Basic .eml Parsing
  - Add `mail-parser` crate dependency
  - Create `parse_eml` function with Email struct
  - Implement error handling for malformed files
  - Write comprehensive unit tests

- [ ] **Task 2.2**: Mbox Parsing
  - Add `mbox` crate dependency
  - Create `parse_mbox` function leveraging `parse_eml`
  - Implement error handling for mbox files
  - Write unit tests for mbox parsing

- [ ] **Task 2.3**: Email Cleaning and Normalization
  - Implement `html_to_plain_text` function
  - Create `strip_signatures_and_quotes` function
  - Update Email struct for cleaned content
  - Write unit tests for cleaning functions

### Phase 3: Data Storage and Indexing
- [ ] **Task 3.1**: PostgreSQL Schema and Rust DB Setup
  - Create migrations directory with SQL schema
  - Add sqlx dependencies with required features
  - Implement Message struct mapping to database
  - Create `establish_connection` and `run_migrations` functions

- [ ] **Task 3.2**: Data Ingestion into Database
  - Implement `insert_message` function
  - Modify parsing functions to return Message structs
  - Create main ingestion script
  - Write integration tests for full pipeline

### Phase 4: Embedding Generation
- [ ] **Task 4.1**: Local Embedding Model Integration (Python)
  - Install sentence-transformers and fastapi
  - Create `embedding_service.py` with FastAPI
  - Load `multilingual-e5-large` model
  - Create `/embed` endpoint with tests

- [ ] **Task 4.2**: Embedding Workflow Integration
  - Add reqwest and tokio dependencies to Rust
  - Create async `get_embedding` function
  - Modify ingestion script for embedding generation
  - Create process management scripts for local service communication

### Phase 5: Desktop Application (UI)
- [ ] **Task 5.1**: Basic Application Structure
  - Initialize Tauri project `desktop_app`
  - Create basic window with title

- [ ] **Task 5.2**: Email Import Functionality
  - Implement file input for .mbox/.eml files
  - Add drag-and-drop functionality
  - Create Tauri command for file processing
  - Connect to ingestion service endpoint

- [ ] **Task 5.3**: Draft Display and Interaction
  - Create UI for processed emails list
  - Implement "Generate Draft" functionality
  - Add draft display area
  - Include "Copy to Clipboard" button

### Phase 6: RAG Pipeline and LLM Integration
- [ ] **Task 6.1**: Vector Search Implementation
  - Create `search_similar_emails` function in Rust
  - Implement cosine similarity search with pgvector
  - Expose `/search_emails` HTTP endpoint

- [ ] **Task 6.2**: Prompt Composition and LLM Integration
  - Install langchain and dependencies
  - Create `/generate_draft` endpoint
  - Implement RAG prompt composition
  - Integrate with local LLM (Ollama/Llama-3)

- [ ] **Task 6.3**: Wiring RAG to Desktop App
  - Connect desktop app to `/generate_draft` endpoint
  - Update UI with generated drafts and citations

### Phase 7: Compliance and Governance Features
- [ ] **Task 7.1**: Output Logging
  - Implement comprehensive logging for all RAG requests
  - Ensure persistent storage with timestamps
  - Include unique transaction IDs

- [ ] **Task 7.2**: Pseudonymisation Placeholder
  - Create `pseudonymizer.py` module
  - Implement placeholder `pseudonymize_text` function
  - Integrate into RAG pipeline for future GDPR compliance

### Phase 8: Local Deployment and Testing
- [ ] **Task 8.1**: Local Process Management and Orchestration
  - Create startup scripts for ingestion_service
  - Create startup scripts for rag_service
  - Create master orchestration script for all services
  - Verify inter-service communication locally

- [ ] **Task 8.2**: Comprehensive Testing
  - Expand unit tests for high coverage
  - Implement integration tests
  - Develop end-to-end tests for desktop app

- [ ] **Task 8.3**: Performance and Security Baselines
  - Conduct performance benchmarks
  - Perform preliminary security review

### Phase 9: Installation Package and Distribution
- [ ] **Task 9.1**: Installation Package Creation
  - Create cross-platform installation scripts (Windows .msi, macOS .pkg, Linux .deb/.rpm)
  - Develop automated dependency installation for all required software
  - Create configuration migration tools for system settings
  - Implement automated database schema setup during installation

- [ ] **Task 9.2**: Distribution and Documentation
  - Create comprehensive installation guides for end users
  - Develop user manuals and troubleshooting guides
  - Create automated update mechanisms for future versions
  - Implement system health checks and diagnostic tools

## Additional Implementation Guidelines

### Architect Mode Instructions
- **Code Quality**: Ensure all implementations follow Rust and Python best practices
- **Error Handling**: Implement comprehensive error handling with proper logging
- **Testing**: Maintain high test coverage with unit, integration, and e2e tests
- **Documentation**: Include inline documentation and README files
- **Performance**: Optimize for the target of processing ~200,000 emails efficiently

### Project Manager Mode Instructions
- **Incremental Development**: Each task must build upon previous completed work
- **No Orphaned Code**: All code must be integrated into the system
- **Local Development Focus**: All services run natively without containerization
- **Process Management**: Ensure proper startup order and inter-service communication
- **Dependency Management**: Ensure proper dependency resolution and version compatibility
- **Compliance First**: GDPR and EU AI Act requirements are non-negotiable
- **Human-in-the-Loop**: Maintain manual review requirement for all generated drafts

### Critical Success Factors
1. **Compliance**: GDPR pseudonymisation and EU AI Act limited-risk classification
2. **Performance**: Sub-second draft generation, efficient bulk email processing
3. **Usability**: Intuitive desktop application for legal advisors
4. **Reliability**: Robust error handling and data integrity
5. **Extensibility**: Modular architecture for future enhancements

### Local Development Requirements
- **PostgreSQL 16**: Local installation with pgvector extension
- **Rust Toolchain**: Latest stable Rust compiler and Cargo
- **Python 3.9+**: With virtual environment support
- **Node.js**: For Tauri desktop application development
- **Ollama**: For local LLM hosting (Llama-3 8B)

### Process Management Strategy
- **Service Startup Order**: PostgreSQL → Embedding Service → Ingestion Service → RAG Service → Desktop App
- **Inter-Process Communication**: HTTP APIs for service communication
- **Configuration Management**: Environment files and configuration directories
- **Logging**: Centralized logging for all services with file-based persistence

### Risk Mitigation
- **Data Privacy**: Implement pseudonymisation before any cloud service interaction
- **AI Safety**: Regular bias and safety evaluations of LLM outputs
- **System Reliability**: Comprehensive testing and monitoring
- **Compliance Auditing**: Maintain detailed logs for 6+ months
- **Security**: Regular vulnerability assessments and secure coding practices
- **Local Security**: Secure local database access and API endpoints
