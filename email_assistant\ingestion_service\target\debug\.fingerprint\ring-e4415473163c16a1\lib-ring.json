{"rustc": 1842507548689473721, "features": "[\"alloc\", \"default\", \"dev_urandom_fallback\"]", "declared_features": "[\"alloc\", \"default\", \"dev_urandom_fallback\", \"less-safe-getrandom-custom-or-rdrand\", \"less-safe-getrandom-espidf\", \"slow_tests\", \"std\", \"test_logging\", \"unstable-testing-arm-no-hw\", \"unstable-testing-arm-no-neon\", \"wasm32_unknown_unknown_js\"]", "target": 13947150742743679355, "profile": 2241668132362809309, "path": 442901360617320951, "deps": [[2828590642173593838, "cfg_if", false, 10118896523543894662], [5491919304041016563, "build_script_build", false, 9755635845892514526], [8995469080876806959, "untrusted", false, 522613454715898202], [9920160576179037441, "getrandom", false, 11689112549979296871]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\ring-e4415473163c16a1\\dep-lib-ring", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}