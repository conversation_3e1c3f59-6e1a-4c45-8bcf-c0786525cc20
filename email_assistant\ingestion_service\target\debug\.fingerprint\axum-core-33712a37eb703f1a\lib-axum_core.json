{"rustc": 1842507548689473721, "features": "[]", "declared_features": "[\"__private_docs\", \"tracing\"]", "target": 2565713999752801252, "profile": 2241668132362809309, "path": 13067367211683519187, "deps": [[784494742817713399, "tower_service", false, 891412978182292050], [1906322745568073236, "pin_project_lite", false, 27178801118851068], [2517136641825875337, "sync_wrapper", false, 15079274337327401480], [7712452662827335977, "tower_layer", false, 14062717672081574200], [7858942147296547339, "rustversion", false, 18095137511007975503], [9010263965687315507, "http", false, 4349961928876268954], [10229185211513642314, "mime", false, 2311114312296159829], [10629569228670356391, "futures_util", false, 1407185829515809506], [11946729385090170470, "async_trait", false, 12880380351208011548], [14084095096285906100, "http_body", false, 14653673679686981800], [16066129441945555748, "bytes", false, 14757824830115038577], [16900715236047033623, "http_body_util", false, 10290862317673768159]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\axum-core-33712a37eb703f1a\\dep-lib-axum_core", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}