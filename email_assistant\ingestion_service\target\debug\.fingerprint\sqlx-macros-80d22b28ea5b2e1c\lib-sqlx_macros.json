{"rustc": 1842507548689473721, "features": "[\"_rt-tokio\", \"_tls-rustls\", \"chrono\", \"default\", \"json\", \"migrate\", \"sqlite\", \"uuid\"]", "declared_features": "[\"_rt-async-std\", \"_rt-tokio\", \"_tls-native-tls\", \"_tls-rustls\", \"bigdecimal\", \"bit-vec\", \"chrono\", \"default\", \"ipnetwork\", \"json\", \"mac_address\", \"migrate\", \"mysql\", \"postgres\", \"rust_decimal\", \"sqlite\", \"time\", \"uuid\"]", "target": 13494433325021527976, "profile": 2225463790103693989, "path": 14125105812391847805, "deps": [[996810380461694889, "sqlx_core", false, 15441323543634552525], [2713742371683562785, "syn", false, 8593241034591342210], [3060637413840920116, "proc_macro2", false, 9742792036988073286], [15733334431800349573, "sqlx_macros_core", false, 15261851759757953540], [17990358020177143287, "quote", false, 675908390363228251]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\sqlx-macros-80d22b28ea5b2e1c\\dep-lib-sqlx_macros", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}