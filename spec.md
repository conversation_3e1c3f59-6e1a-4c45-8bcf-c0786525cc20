# AI-Assisted Email Response System - Functional Specification

## 1. Introduction

This document outlines the functional and non-functional requirements for an AI-assisted email response system designed for legal advisors. The system aims to streamline the email response process by leveraging retrieval-augmented generation (RAG) to suggest draft replies based on historical email content.

## 2. System Goals

*   **Automate Draft Generation:** Automatically generate relevant and contextually appropriate email draft replies for incoming legal correspondence.
*   **Improve Response Efficiency:** Reduce the time legal advisors spend drafting routine email responses.
*   **Ensure Compliance:** Adhere to GDPR and EU AI Act regulations regarding data privacy, transparency, and accountability.
*   **Scalability:** Handle a large volume of emails (~200,000) and scale to accommodate future growth.
*   **High Performance:** Utilize performance-oriented technologies (e.g., Rust) for core processing to ensure fast ingestion and draft generation.

## 3. User Stories

*   **As a Legal Advisor,** I want to manually select emails from my Thunderbird client and initiate their processing so that I can get AI-generated draft replies.
*   **As a Legal Advisor,** I want to drag-and-drop email files (e.g., .mbox, .eml) into a standalone desktop application so that they can be ingested and processed by the system.
*   **As a Legal Advisor,** I want to view the AI-generated draft replies within the standalone desktop application so that I can review, edit, and manually copy them into my Thunderbird client.
*   **As a Legal Advisor,** I need the system to suggest replies that are relevant to the email's content and informed by historical legal correspondence.
*   **As a Legal Advisor,** I need to be confident that my client's data is handled in compliance with GDPR and EU AI Act regulations.

## 4. Architecture Choices

### 4.1. Core Components

*   **Ingestion Component:** Responsible for parsing emails from mbox files or raw .eml formats. This component will be implemented primarily in Rust for speed.
*   **Normalization & Enrichment Component:** Converts HTML to plain text, performs thread detection, and auto-labels emails (matter type, urgency, jurisdiction).
*   **Embedding Component:** Generates vector embeddings for email content using selected embedding models (e.g., `multilingual-e5-large` locally or `text-embedding-3-large` via cloud).
*   **Storage Layer:**
    *   **Relational Database (PostgreSQL 16):** Stores email metadata (`id`, `thread_id`, `subject`, `from`, `to`, `date`, `path_raw_eml`, etc.).
    *   **Vector Database (pgvector / Qdrant / Milvus):** Stores dense vector embeddings, supporting hybrid BM25 + semantic search.
    *   **Metadata Search (Postgres `tsvector` or OpenSearch):** Facilitates legal discovery.
*   **Draft Generation (RAG) Pipeline:**
    *   **Retrieval:** Retrieves top-k relevant documents (emails) based on the incoming email's content.
    *   **Prompt Composition:** Constructs LLM prompts incorporating the retrieved documents, the incoming email, and policy guidelines.
    *   **LLM Generation:** Utilizes either GPT-4o/GPT-4o-mini (cloud) or a local Llama-3 model to generate draft replies.
    *   **Source Citation:** Returns citations for transparency and auditability.
*   **Desktop Application (UI Layer):** A standalone application for manual email ingestion (drag-and-drop, import) and displaying generated drafts for review and copy-pasting.

### 4.2. Technology Stack (Preferred)

*   **Primary Development Language:** Rust (for performance-critical parts like ingestion and parsing), Python (for orchestration, LLM integration, and potentially the desktop app if a suitable framework exists).
*   **Email Parsing:** `mail-parser` crate (Rust) or Apache Tika MboxParser (via JNI/CLI).
*   **Signature Stripping:** `mailgun/talon`.
*   **Embedding Models:** `multilingual-e5-large` or `bge-m3` (local), `text-embedding-3-large` (cloud).
*   **LLMs:** GPT-4o / GPT-4o-mini (cloud), Llama-3 8B (local).
*   **RAG Framework:** LangChain / LlamaIndex (primarily for Python components).
*   **Database:** PostgreSQL 16 with pgvector extension (preferred for vectors due to simplicity and hybrid search capabilities).
*   **Deployment:** Local development environment with native installations and process management.

## 5. Data Handling Details

### 5.1. Data Ingestion

*   Emails will be ingested from local Thunderbird mbox files or raw .eml files via the desktop application.
*   Initial ingestion will involve parsing, cleaning (stripping signatures/quotes), and converting HTML to plain text.
*   Raw .eml files will be stored (e.g., in S3 or local storage) for archival and re-processing if needed.

### 5.2. Data Storage

*   **PostgreSQL (Local Installation):**
    *   `messages` table: `id`, `thread_id`, `subject`, `from`, `to`, `date`, `path_raw_eml`, `plain_text_content`, `classification_labels` (matter type, urgency, jurisdiction), etc.
    *   `vectors` table (if pgvector is used): `message_id`, `embedding`.
*   **Vector Database (pgvector extension):** Stores dense vector embeddings with efficient indexing for similarity search using local PostgreSQL instance.

### 5.3. Data Flow for Draft Generation

1.  User selects an email in the desktop application and triggers draft generation.
2.  The system extracts the content of the selected email.
3.  The email content is embedded into a vector.
4.  This vector is used to retrieve top-k similar emails from the vector database.
5.  The retrieved emails and the original email are used to compose a prompt for the LLM.
6.  The LLM generates a draft reply.
7.  The draft reply, along with citations to the source emails, is returned to the desktop application.

## 6. Error Handling Strategies

*   **Ingestion Errors:**
    *   Malformed email files: Log errors, skip problematic files, and notify the user.
    *   Parsing failures: Attempt fallback parsing methods; if persistent, log and quarantine the email.
*   **Database Errors:**
    *   Connection failures: Implement retry mechanisms with exponential backoff.
    *   Data integrity issues: Validate data before insertion; use database transactions for atomicity.
*   **LLM API Errors:**
    *   Rate limiting: Implement robust rate limiting and retry logic.
    *   API unavailability: Fallback to local LLM if configured; notify user if no alternative.
    *   Poor quality drafts: Provide mechanisms for user feedback to improve future generations (fine-tuning data).
*   **General System Errors:**
    *   Comprehensive logging and monitoring for all components.
    *   Alerting for critical failures (e.g., ingestion pipeline halt, database down).
    *   Graceful degradation where possible (e.g., if embedding service is down, use a simpler keyword search).

## 7. Compliance Obligations

### 7.1. GDPR

*   **Pseudonymisation:** Implement mechanisms to pseudonymise personal data (e.g., names, email addresses, sensitive identifiers) before sending data to cloud-based LLMs or embedding services.
*   **Data Minimisation:** Only process and store data strictly necessary for the system's function.
*   **Data Subject Rights:** Design the system to support data subject rights (access, rectification, erasure) by providing tools to manage stored email content and metadata.

### 7.2. EU AI Act

*   **Human-in-the-Loop:** The system is designed as a "limited-risk" AI system, as human legal advisors are always in the loop for reviewing and sending generated drafts. Automatic sending is explicitly avoided to prevent "high-risk" classification.
*   **Output Logging:** Maintain logs of generated drafts and associated inputs for a minimum of 6 months to ensure accountability and auditability.
*   **Bias and Safety Evaluations:** Implement regular evaluations (e.g., using tools like Llama-Guard 3) to detect and mitigate potential biases or unsafe outputs from the LLMs.
*   **Risk Assessment Reports:** Produce regular risk assessment reports detailing compliance measures and findings.

## 8. Non-Functional Requirements

*   **Performance:**
    *   Email ingestion: Process ~200,000 emails efficiently (target: completion within a reasonable timeframe, e.g., 24-48 hours for initial bulk import, near real-time for new emails).
    *   Draft generation: Generate drafts within seconds of a request.
*   **Security:**
    *   Data encryption at rest and in transit.
    *   Secure access controls for the database and application components.
    *   Regular security audits and vulnerability assessments.
*   **Reliability:** High availability for core services (database, LLM integration).
*   **Maintainability:** Modular architecture, clear code, comprehensive documentation, and automated testing.
*   **Extensibility:** Designed to easily integrate new embedding models, LLMs, or vector databases.
*   **Usability:** Intuitive desktop application interface for legal advisors.