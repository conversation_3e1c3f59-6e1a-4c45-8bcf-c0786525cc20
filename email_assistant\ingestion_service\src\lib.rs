// AI-Assisted Email Response System - Ingestion Service Library
// Core functionality for email parsing, cleaning, and database operations

use std::fs;
use std::io;
use uuid::Uuid;
use chrono::{DateTime, Utc};
use serde::{Serialize, Deserialize};
use sqlx::{PgPool, Row};
use dotenv::dotenv;
use reqwest::Client;

// Email parsing and cleaning functions

/// Converts HTML content to plain text with proper formatting.
/// Uses html2text crate for robust HTML parsing and conversion.
pub fn html_to_plain_text(html: &str) -> String {
    html2text::from_read(html.as_bytes(), 80)
}

/// Strips email signatures and quoted content from email text.
/// Removes lines starting with '>' (quoted content) and content after signature markers.
pub fn strip_signatures_and_quotes(text: &str) -> String {
    let mut cleaned_lines = Vec::new();
    
    for line in text.lines() {
        // Check for common signature markers
        if line.trim() == "--" || 
           (line.trim().starts_with("-- ") && line.trim().len() < 50) ||
           line.trim().starts_with("---") ||
           line.contains("Sent from my") ||
           line.contains("Best regards") ||
           line.contains("Kind regards") {
            break; // Stop processing once we hit a signature
        }
        
        // Skip quoted content (lines starting with >)
        if line.starts_with('>') || line.starts_with(" >") {
            continue;
        }
        
        // Skip forwarded message headers
        if line.starts_with("-----Original Message-----") ||
           line.starts_with("From:") && cleaned_lines.is_empty() {
            continue;
        }
        
        cleaned_lines.push(line);
    }
    
    // Remove excessive whitespace at the end
    while let Some(last_line) = cleaned_lines.last() {
        if last_line.trim().is_empty() {
            cleaned_lines.pop();
        } else {
            break;
        }
    }
    
    cleaned_lines.join("\n")
}

// Email struct for parsed emails
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct ParsedEmail {
    pub id: Uuid,
    pub subject: Option<String>,
    pub from: Option<String>,
    pub to: Vec<String>,
    pub sent_date: Option<DateTime<Utc>>,
    pub plain_text_body_raw: Option<String>,
    pub html_body_raw: Option<String>,
    pub cleaned_plain_text_body: Option<String>,
}

// Database Message struct for database operations
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Message {
    pub id: Uuid,
    pub subject: Option<String>,
    pub from_address: Option<String>,
    pub to_addresses: Vec<String>,
    pub sent_date: Option<DateTime<Utc>>,
    pub plain_text_body_raw: Option<String>,
    pub html_body_raw: Option<String>,
    pub cleaned_plain_text_body: Option<String>,
    pub file_path: Option<String>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

impl From<ParsedEmail> for Message {
    fn from(parsed: ParsedEmail) -> Self {
        let now = Utc::now();
        Message {
            id: parsed.id,
            subject: parsed.subject,
            from_address: parsed.from,
            to_addresses: parsed.to,
            sent_date: parsed.sent_date,
            plain_text_body_raw: parsed.plain_text_body_raw,
            html_body_raw: parsed.html_body_raw,
            cleaned_plain_text_body: parsed.cleaned_plain_text_body,
            file_path: None,
            created_at: now,
            updated_at: now,
        }
    }
}

/// Parses a single .eml file into a ParsedEmail struct.
/// Applies cleaning functions and handles errors.
pub fn parse_eml(file_path: &str) -> Result<ParsedEmail, io::Error> {
    let content = fs::read_to_string(file_path)?;
    parse_email_from_string(&content)
}

/// Parses an mbox file and returns a vector of ParsedEmail structs.
/// Uses a simple approach to split mbox content by "From " lines.
pub fn parse_mbox(file_path: &str) -> Result<Vec<ParsedEmail>, io::Error> {
    let content = fs::read_to_string(file_path)?;
    let mut emails = Vec::new();
    
    // Split mbox content by "From " lines (mbox format separator)
    let mut current_email = String::new();
    let mut in_email = false;
    
    for line in content.lines() {
        if line.starts_with("From ") && line.contains("@") {
            // Process the previous email if we have one
            if in_email && !current_email.trim().is_empty() {
                match parse_email_from_string(&current_email) {
                    Ok(parsed_email) => emails.push(parsed_email),
                    Err(e) => {
                        eprintln!("Warning: Failed to parse email in mbox: {}", e);
                        // Continue processing other emails
                    }
                }
            }
            // Start a new email
            current_email.clear();
            in_email = true;
        } else if in_email {
            current_email.push_str(line);
            current_email.push('\n');
        }
    }
    
    // Process the last email
    if in_email && !current_email.trim().is_empty() {
        match parse_email_from_string(&current_email) {
            Ok(parsed_email) => emails.push(parsed_email),
            Err(e) => {
                eprintln!("Warning: Failed to parse last email in mbox: {}", e);
            }
        }
    }
    
    Ok(emails)
}

/// Helper function to parse email from string content (extracted from parse_eml)
fn parse_email_from_string(content: &str) -> Result<ParsedEmail, io::Error> {
    let mut subject = None;
    let mut from = None;
    let mut to = Vec::new();
    let mut body_start = 0;
    
    for (i, line) in content.lines().enumerate() {
        if line.is_empty() {
            body_start = i + 1;
            break;
        }
        
        if line.to_lowercase().starts_with("subject:") {
            subject = Some(line[8..].trim().to_string());
        } else if line.to_lowercase().starts_with("from:") {
            from = Some(line[5..].trim().to_string());
        } else if line.to_lowercase().starts_with("to:") {
            to.push(line[3..].trim().to_string());
        }
    }
    
    let body: String = content.lines().skip(body_start).collect::<Vec<_>>().join("\n");
    let cleaned_body = strip_signatures_and_quotes(&body);

    Ok(ParsedEmail {
        id: Uuid::new_v4(),
        subject,
        from,
        to,
        sent_date: None, // Will implement proper date parsing later
        plain_text_body_raw: Some(body.clone()),
        html_body_raw: None,
        cleaned_plain_text_body: Some(cleaned_body),
    })
}

/// Establishes a connection to the PostgreSQL database.
/// Reads the DATABASE_URL from environment variables.
pub async fn establish_connection() -> Result<PgPool, sqlx::Error> {
    dotenv().ok(); // Load .env file if present
    
    let database_url = std::env::var("DATABASE_URL")
        .unwrap_or_else(|_| "postgresql://email_user:email_password@localhost:5432/email_db".to_string());
    
    PgPool::connect(&database_url).await
}

/// Runs database migrations to set up the schema.
pub async fn run_migrations(pool: &PgPool) -> Result<(), sqlx::Error> {
    // Read and execute the migration file
    let migration_sql = fs::read_to_string("migrations/001_initial_schema.sql")
        .map_err(|e| sqlx::Error::Io(e))?;
    
    // Execute the migration
    sqlx::raw_sql(&migration_sql).execute(pool).await?;
    
    println!("Database migrations completed successfully");
    Ok(())
}

/// Inserts a message into the database.
pub async fn insert_message(pool: &PgPool, message: &Message) -> Result<Uuid, sqlx::Error> {
    let result = sqlx::query(
        r#"
        INSERT INTO messages (id, subject, from_address, to_addresses, sent_date, 
                            plain_text_body_raw, html_body_raw, cleaned_plain_text_body, file_path)
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
        RETURNING id
        "#
    )
    .bind(message.id)
    .bind(&message.subject)
    .bind(&message.from_address)
    .bind(&message.to_addresses)
    .bind(message.sent_date)
    .bind(&message.plain_text_body_raw)
    .bind(&message.html_body_raw)
    .bind(&message.cleaned_plain_text_body)
    .bind(&message.file_path)
    .fetch_one(pool)
    .await?;

    Ok(result.get("id"))
}

/// Retrieves messages from the database with optional filtering.
pub async fn get_messages(pool: &PgPool, limit: Option<i64>) -> Result<Vec<Message>, sqlx::Error> {
    let limit = limit.unwrap_or(100);
    
    let rows = sqlx::query(
        r#"
        SELECT id, subject, from_address, to_addresses, sent_date,
               plain_text_body_raw, html_body_raw, cleaned_plain_text_body,
               file_path, created_at, updated_at
        FROM messages
        ORDER BY created_at DESC
        LIMIT $1
        "#
    )
    .bind(limit)
    .fetch_all(pool)
    .await?;

    let mut messages = Vec::new();
    for row in rows {
        let message = Message {
            id: row.get("id"),
            subject: row.get("subject"),
            from_address: row.get("from_address"),
            to_addresses: row.get("to_addresses"),
            sent_date: row.get("sent_date"),
            plain_text_body_raw: row.get("plain_text_body_raw"),
            html_body_raw: row.get("html_body_raw"),
            cleaned_plain_text_body: row.get("cleaned_plain_text_body"),
            file_path: row.get("file_path"),
            created_at: row.get("created_at"),
            updated_at: row.get("updated_at"),
        };
        messages.push(message);
    }

    Ok(messages)
}

/// Processes a single email file and inserts it into the database.
pub async fn ingest_email_file(pool: &PgPool, file_path: &str) -> Result<Uuid, Box<dyn std::error::Error>> {
    let parsed_email = if file_path.ends_with(".mbox") {
        // For mbox files, take the first email for now
        let emails = parse_mbox(file_path)?;
        if emails.is_empty() {
            return Err("No emails found in mbox file".into());
        }
        emails.into_iter().next().unwrap()
    } else {
        parse_eml(file_path)?
    };

    let mut message = Message::from(parsed_email);
    message.file_path = Some(file_path.to_string());

    let message_id = insert_message(pool, &message).await?;
    println!("✓ Ingested email: {} (ID: {})", file_path, message_id);
    
    Ok(message_id)
}

/// Processes multiple email files from a directory.
pub async fn ingest_directory(pool: &PgPool, dir_path: &str) -> Result<Vec<Uuid>, Box<dyn std::error::Error>> {
    let mut ingested_ids = Vec::new();
    
    for entry in fs::read_dir(dir_path)? {
        let entry = entry?;
        let path = entry.path();
        
        if let Some(extension) = path.extension() {
            if extension == "eml" || extension == "mbox" {
                if let Some(path_str) = path.to_str() {
                    match ingest_email_file(pool, path_str).await {
                        Ok(id) => ingested_ids.push(id),
                        Err(e) => eprintln!("Warning: Failed to ingest {}: {}", path_str, e),
                    }
                }
            }
        }
    }
    
    println!("✓ Ingested {} emails from directory: {}", ingested_ids.len(), dir_path);
    Ok(ingested_ids)
}

/// Processes all emails from an mbox file and inserts them into the database.
pub async fn ingest_mbox_file(pool: &PgPool, file_path: &str) -> Result<Vec<Uuid>, Box<dyn std::error::Error>> {
    let emails = parse_mbox(file_path)?;
    let mut ingested_ids = Vec::new();
    
    for parsed_email in emails {
        let mut message = Message::from(parsed_email);
        message.file_path = Some(file_path.to_string());
        
        match insert_message(pool, &message).await {
            Ok(id) => ingested_ids.push(id),
            Err(e) => eprintln!("Warning: Failed to insert email: {}", e),
        }
    }
    
    println!("✓ Ingested {} emails from mbox: {}", ingested_ids.len(), file_path);
    Ok(ingested_ids)
}

// Embedding service integration

#[derive(Debug, Serialize)]
struct EmbeddingRequest {
    text: String,
    normalize: Option<bool>,
}

#[derive(Debug, Deserialize)]
struct EmbeddingResponse {
    embedding: Vec<f32>,
    model_name: String,
    dimension: usize,
}

/// Generates an embedding for the given text using the embedding service.
pub async fn get_embedding(text: &str, embedding_service_url: &str) -> Result<Vec<f32>, Box<dyn std::error::Error>> {
    let client = Client::new();

    let request = EmbeddingRequest {
        text: text.to_string(),
        normalize: Some(true),
    };

    let response = client
        .post(&format!("{}/embed", embedding_service_url))
        .json(&request)
        .send()
        .await?;

    if response.status().is_success() {
        let embedding_response: EmbeddingResponse = response.json().await?;
        Ok(embedding_response.embedding)
    } else {
        let error_text = response.text().await?;
        Err(format!("Embedding service error: {}", error_text).into())
    }
}

/// Inserts an embedding into the database.
pub async fn insert_embedding(pool: &PgPool, message_id: Uuid, embedding: &[f32], model_name: &str) -> Result<Uuid, sqlx::Error> {
    // Convert f32 vector to pgvector format
    let embedding_str = format!("[{}]", embedding.iter().map(|x| x.to_string()).collect::<Vec<_>>().join(","));

    let result = sqlx::query(
        r#"
        INSERT INTO embeddings (message_id, embedding, embedding_model)
        VALUES ($1, $2, $3)
        RETURNING id
        "#
    )
    .bind(message_id)
    .bind(embedding_str)
    .bind(model_name)
    .fetch_one(pool)
    .await?;

    Ok(result.get("id"))
}

/// Processes a single email file, inserts it into the database, and generates embeddings.
pub async fn ingest_email_file_with_embeddings(
    pool: &PgPool,
    file_path: &str,
    embedding_service_url: &str
) -> Result<(Uuid, Option<Uuid>), Box<dyn std::error::Error>> {
    let parsed_email = if file_path.ends_with(".mbox") {
        // For mbox files, take the first email for now
        let emails = parse_mbox(file_path)?;
        if emails.is_empty() {
            return Err("No emails found in mbox file".into());
        }
        emails.into_iter().next().unwrap()
    } else {
        parse_eml(file_path)?
    };

    let mut message = Message::from(parsed_email);
    message.file_path = Some(file_path.to_string());

    // Insert the message first
    let message_id = insert_message(pool, &message).await?;
    println!("✓ Ingested email: {} (ID: {})", file_path, message_id);

    // Generate embedding for the cleaned text
    let embedding_id = if let Some(cleaned_text) = &message.cleaned_plain_text_body {
        if !cleaned_text.trim().is_empty() {
            match get_embedding(cleaned_text, embedding_service_url).await {
                Ok(embedding) => {
                    match insert_embedding(pool, message_id, &embedding, "sentence-transformers/all-MiniLM-L6-v2").await {
                        Ok(emb_id) => {
                            println!("✓ Generated embedding for email: {}", message_id);
                            Some(emb_id)
                        }
                        Err(e) => {
                            eprintln!("Warning: Failed to insert embedding: {}", e);
                            None
                        }
                    }
                }
                Err(e) => {
                    eprintln!("Warning: Failed to generate embedding: {}", e);
                    None
                }
            }
        } else {
            println!("⚠ Skipping embedding for empty text: {}", message_id);
            None
        }
    } else {
        println!("⚠ No cleaned text available for embedding: {}", message_id);
        None
    };

    Ok((message_id, embedding_id))
}

/// Processes all emails from an mbox file and inserts them into the database with embeddings.
pub async fn ingest_mbox_file_with_embeddings(
    pool: &PgPool,
    file_path: &str,
    embedding_service_url: &str
) -> Result<Vec<(Uuid, Option<Uuid>)>, Box<dyn std::error::Error>> {
    let emails = parse_mbox(file_path)?;
    let mut ingested_results = Vec::new();

    for parsed_email in emails {
        let mut message = Message::from(parsed_email);
        message.file_path = Some(file_path.to_string());

        match insert_message(pool, &message).await {
            Ok(message_id) => {
                println!("✓ Ingested email from mbox: {}", message_id);

                // Generate embedding
                let embedding_id = if let Some(cleaned_text) = &message.cleaned_plain_text_body {
                    if !cleaned_text.trim().is_empty() {
                        match get_embedding(cleaned_text, embedding_service_url).await {
                            Ok(embedding) => {
                                match insert_embedding(pool, message_id, &embedding, "sentence-transformers/all-MiniLM-L6-v2").await {
                                    Ok(emb_id) => {
                                        println!("✓ Generated embedding for email: {}", message_id);
                                        Some(emb_id)
                                    }
                                    Err(e) => {
                                        eprintln!("Warning: Failed to insert embedding: {}", e);
                                        None
                                    }
                                }
                            }
                            Err(e) => {
                                eprintln!("Warning: Failed to generate embedding: {}", e);
                                None
                            }
                        }
                    } else {
                        None
                    }
                } else {
                    None
                };

                ingested_results.push((message_id, embedding_id));
            }
            Err(e) => eprintln!("Warning: Failed to insert email: {}", e),
        }
    }

    println!("✓ Ingested {} emails from mbox with embeddings: {}", ingested_results.len(), file_path);
    Ok(ingested_results)
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::io::Write;
    use tempfile::NamedTempFile;

    #[test]
    fn test_html_to_plain_text() {
        let html_content = "<html><body><h1>Hello</h1><p>This is <b>HTML</b> with a <a href=\"http://example.com\">link</a>.</p><ul><li>Item 1</li><li>Item 2</li></ul></body></html>";
        let plain_text = html_to_plain_text(html_content);
        assert!(plain_text.contains("Hello"));
        assert!(plain_text.contains("HTML"));
        assert!(plain_text.contains("Item 1"));
        assert!(plain_text.contains("Item 2"));
    }

    #[test]
    fn test_strip_signatures_and_quotes() {
        let text_with_signature = "Hello,\n\nThis is the main body.\n\n-- \nJohn Doe\nSoftware Engineer";
        let cleaned = strip_signatures_and_quotes(text_with_signature);
        assert_eq!(cleaned, "Hello,\n\nThis is the main body.");

        let text_with_quote = "Hello,\n\nMy reply.\n\n> On Mon, Jan 1, 2023 at 10:00 AM John Doe <<EMAIL>> wrote:\n> This is a quoted message.";
        let cleaned_quote = strip_signatures_and_quotes(text_with_quote);
        assert_eq!(cleaned_quote, "Hello,\n\nMy reply.");
    }

    #[test]
    fn test_parse_valid_eml() -> Result<(), Box<dyn std::error::Error>> {
        let eml_content = "From: <EMAIL>\nSubject: Test Subject\nTo: <EMAIL>\n\nHello, this is a test email.";
        let mut file = NamedTempFile::new()?;
        file.write_all(eml_content.as_bytes())?;
        let file_path = file.path().to_str().unwrap();

        let parsed_email = parse_eml(file_path)?;
        assert_eq!(parsed_email.subject, Some("Test Subject".to_string()));
        assert_eq!(parsed_email.from, Some("<EMAIL>".to_string()));
        assert_eq!(parsed_email.to, vec!["<EMAIL>".to_string()]);
        assert!(parsed_email.cleaned_plain_text_body.is_some());
        Ok(())
    }

    #[test]
    fn test_parse_mbox_single_email() -> Result<(), Box<dyn std::error::Error>> {
        let mbox_content = "From <EMAIL> Mon Jan  1 10:00:00 2024\nFrom: <EMAIL>\nSubject: Test Subject\nTo: <EMAIL>\n\nHello, this is a test email.\n\n";
        let mut file = NamedTempFile::new()?;
        file.write_all(mbox_content.as_bytes())?;
        let file_path = file.path().to_str().unwrap();

        let parsed_emails = parse_mbox(file_path)?;
        assert_eq!(parsed_emails.len(), 1);

        let email = &parsed_emails[0];
        assert_eq!(email.subject, Some("Test Subject".to_string()));
        assert_eq!(email.from, Some("<EMAIL>".to_string()));
        assert_eq!(email.to, vec!["<EMAIL>".to_string()]);
        assert!(email.cleaned_plain_text_body.is_some());
        Ok(())
    }
}
