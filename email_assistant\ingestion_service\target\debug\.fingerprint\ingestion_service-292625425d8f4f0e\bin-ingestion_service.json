{"rustc": 1842507548689473721, "features": "[]", "declared_features": "[]", "target": 4578055519624627116, "profile": 8731458305071235362, "path": 4942398508502643691, "deps": [[722509084658410873, "ingestion_service", false, 15491915460238080350], [1441306149310335789, "tempfile", false, 8249039906580876076], [1760623714118191065, "dotenv", false, 15522334084148677918], [3445762082367621908, "qdrant_client", false, 10959041908521309490], [7244058819997729774, "reqwest", false, 17853322145005744044], [8319709847752024821, "uuid", false, 15569704690176671513], [9689903380558560274, "serde", false, 16584340690927804728], [9897246384292347999, "chrono", false, 9740778159667974192], [12393800526703971956, "tokio", false, 1898190954426490280], [15367738274754116744, "serde_json", false, 11360680293418975206], [17565085470850576164, "html2text", false, 12414929624477428855]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\ingestion_service-292625425d8f4f0e\\dep-bin-ingestion_service", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}