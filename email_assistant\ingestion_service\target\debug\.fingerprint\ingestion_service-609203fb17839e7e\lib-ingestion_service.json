{"rustc": 1842507548689473721, "features": "[]", "declared_features": "[]", "target": 4978127743549002298, "profile": 17672942494452627365, "path": 10763286916239946207, "deps": [[1441306149310335789, "tempfile", false, 6853782238214846084], [1760623714118191065, "dotenv", false, 4035044196166406372], [7244058819997729774, "reqwest", false, 3008359355660368518], [8319709847752024821, "uuid", false, 1910560137687000915], [9689903380558560274, "serde", false, 12682574395712889020], [9897246384292347999, "chrono", false, 13055332157358921875], [10632374999838431203, "sqlx", false, 420519193129359943], [12393800526703971956, "tokio", false, 15081573088802642062], [17565085470850576164, "html2text", false, 5819541003975772101]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\ingestion_service-609203fb17839e7e\\dep-lib-ingestion_service", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}