# AI-Assisted Email Response System - RAG Service
# This service handles embedding generation and retrieval-augmented generation for email responses

from fastapi import <PERSON><PERSON><PERSON>, HTTPException
from pydantic import BaseModel
from typing import List, Optional, Dict, Any
import uvicorn
import logging
from sentence_transformers import SentenceTransformer
import numpy as np
import os
from dotenv import load_dotenv
import asyncio
from contextlib import asynccontextmanager

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Global model variable
model = None

@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup: Load the embedding model
    global model
    logger.info("Loading embedding model...")
    try:
        model_name = os.getenv("EMBEDDING_MODEL", "sentence-transformers/all-MiniLM-L6-v2")
        model = SentenceTransformer(model_name)
        logger.info(f"✓ Embedding model '{model_name}' loaded successfully")
    except Exception as e:
        logger.error(f"Failed to load embedding model: {e}")
        raise

    yield

    # Shutdown: Clean up resources
    logger.info("Shutting down embedding service...")

app = FastAPI(
    title="Email AI Assistant - RAG Service",
    version="1.0.0",
    description="Embedding generation and retrieval service for email AI assistant",
    lifespan=lifespan
)

# Pydantic models
class EmbeddingRequest(BaseModel):
    text: str
    normalize: Optional[bool] = True

class EmbeddingResponse(BaseModel):
    embedding: List[float]
    model_name: str
    dimension: int

class BatchEmbeddingRequest(BaseModel):
    texts: List[str]
    normalize: Optional[bool] = True

class BatchEmbeddingResponse(BaseModel):
    embeddings: List[List[float]]
    model_name: str
    dimension: int
    count: int

class EmailQuery(BaseModel):
    query: str
    max_results: Optional[int] = 5

class EmailResponse(BaseModel):
    id: str
    subject: str
    content: str
    similarity_score: float

@app.get("/")
async def root():
    return {
        "message": "Email AI Assistant RAG Service is running",
        "model_loaded": model is not None,
        "model_name": getattr(model, 'model_name', 'Unknown') if model else None
    }

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy" if model is not None else "unhealthy",
        "model_loaded": model is not None,
        "service": "embedding_service"
    }

@app.post("/embed", response_model=EmbeddingResponse)
async def generate_embedding(request: EmbeddingRequest):
    """Generate embedding for a single text"""
    if model is None:
        raise HTTPException(status_code=503, detail="Embedding model not loaded")

    try:
        # Generate embedding
        embedding = model.encode(request.text, normalize_embeddings=request.normalize)

        # Convert to list for JSON serialization
        embedding_list = embedding.tolist()

        return EmbeddingResponse(
            embedding=embedding_list,
            model_name=getattr(model, 'model_name', 'unknown'),
            dimension=len(embedding_list)
        )
    except Exception as e:
        logger.error(f"Error generating embedding: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to generate embedding: {str(e)}")

@app.post("/embed_batch", response_model=BatchEmbeddingResponse)
async def generate_batch_embeddings(request: BatchEmbeddingRequest):
    """Generate embeddings for multiple texts"""
    if model is None:
        raise HTTPException(status_code=503, detail="Embedding model not loaded")

    if len(request.texts) > 100:  # Limit batch size
        raise HTTPException(status_code=400, detail="Batch size too large (max 100)")

    try:
        # Generate embeddings
        embeddings = model.encode(request.texts, normalize_embeddings=request.normalize)

        # Convert to list for JSON serialization
        embeddings_list = embeddings.tolist()

        return BatchEmbeddingResponse(
            embeddings=embeddings_list,
            model_name=getattr(model, 'model_name', 'unknown'),
            dimension=len(embeddings_list[0]) if embeddings_list else 0,
            count=len(embeddings_list)
        )
    except Exception as e:
        logger.error(f"Error generating batch embeddings: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to generate embeddings: {str(e)}")

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8003)
