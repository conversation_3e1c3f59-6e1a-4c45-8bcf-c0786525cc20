{"rustc": 1842507548689473721, "features": "[\"_rt-tokio\", \"_tls-rustls\", \"chrono\", \"default\", \"json\", \"migrate\", \"postgres\", \"sqlx-postgres\", \"tokio\", \"uuid\"]", "declared_features": "[\"_rt-async-std\", \"_rt-tokio\", \"_tls-native-tls\", \"_tls-rustls\", \"async-std\", \"bigdecimal\", \"bit-vec\", \"chrono\", \"default\", \"ipnetwork\", \"json\", \"mac_address\", \"migrate\", \"mysql\", \"postgres\", \"rust_decimal\", \"sqlite\", \"sqlx-mysql\", \"sqlx-postgres\", \"sqlx-sqlite\", \"time\", \"tokio\", \"uuid\"]", "target": 961973412475639632, "profile": 2225463790103693989, "path": 18389220411245665339, "deps": [[530211389790465181, "hex", false, 9910414242004828434], [996810380461694889, "sqlx_core", false, 12152471045853702521], [1441306149310335789, "tempfile", false, 9822228830468181875], [2713742371683562785, "syn", false, 8593241034591342210], [3060637413840920116, "proc_macro2", false, 9742792036988073286], [3150220818285335163, "url", false, 3146522262934261046], [3405707034081185165, "dotenvy", false, 17790133483060488077], [3722963349756955755, "once_cell", false, 14521976164538839542], [8045585743974080694, "heck", false, 17511591055440357725], [9689903380558560274, "serde", false, 16546253847386255219], [9857275760291862238, "sha2", false, 13375919396894932087], [12170264697963848012, "either", false, 10799552135219786513], [12393800526703971956, "tokio", false, 14974634602880665938], [15367738274754116744, "serde_json", false, 2903224897898545405], [15634168271133386882, "sqlx_postgres", false, 10177659373522056473], [17990358020177143287, "quote", false, 675908390363228251]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\sqlx-macros-core-791e61f9a23d107d\\dep-lib-sqlx_macros_core", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}