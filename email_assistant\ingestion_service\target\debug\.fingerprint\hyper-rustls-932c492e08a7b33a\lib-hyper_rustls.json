{"rustc": 1842507548689473721, "features": "[\"http1\", \"http2\", \"ring\", \"tls12\", \"webpki-roots\", \"webpki-tokio\"]", "declared_features": "[\"aws-lc-rs\", \"default\", \"fips\", \"http1\", \"http2\", \"log\", \"logging\", \"native-tokio\", \"ring\", \"rustls-native-certs\", \"rustls-platform-verifier\", \"tls12\", \"webpki-roots\", \"webpki-tokio\"]", "target": 12220062926890100908, "profile": 2241668132362809309, "path": 632669836025513150, "deps": [[784494742817713399, "tower_service", false, 891412978182292050], [2883436298747778685, "pki_types", false, 1035697240254743143], [5907992341687085091, "webpki_roots", false, 10436398343254151568], [9010263965687315507, "http", false, 4349961928876268954], [11895591994124935963, "tokio_rustls", false, 453299351287021380], [11957360342995674422, "hyper", false, 8140407445045656268], [12393800526703971956, "tokio", false, 6137508459591548543], [16400140949089969347, "rustls", false, 11496959222346507727], [16680807377217054954, "hyper_util", false, 2249913128436515471]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\hyper-rustls-932c492e08a7b33a\\dep-lib-hyper_rustls", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}