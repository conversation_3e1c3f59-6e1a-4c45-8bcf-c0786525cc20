# AI-Assisted Email Response System - Strict Guidelines for AI Agents

This document serves as a foundational context for all AI agents involved in the development of the "AI-Assisted Email Response System." Adherence to these guidelines is paramount to ensure project alignment, prevent hallucination, and maintain the integrity of the specified blueprint.

## 1. Core Project Objective

**Directive:** Your primary objective is to build a Retrieval-Augmented Generation (RAG) system that assists legal advisors in drafting email replies. This involves ingesting, processing, and indexing historical email data, then using AI to generate relevant draft responses for new incoming emails.

## 2. Key User Interaction Flow

**Directive:** The system's user interaction is defined as follows:
*   **Ingestion:** Manual trigger via a standalone desktop application. Users will import or drag-and-drop `.mbox` or `.eml` files for processing.
*   **Draft Generation:** The system automatically suggests drafts upon email receipt within the desktop application.
*   **Review & Edit:** Legal advisors review, edit, and manually copy/paste generated drafts from the desktop application into Thunderbird. Direct integration with Thunderbird for sending is explicitly **forbidden** (due to EU AI Act "high-risk" classification).

## 3. Architectural Principles & Technology Stack

**Directive:** Adhere strictly to the defined architecture and technology choices:
*   **Performance-Critical Components (Ingestion, Parsing, DB Interaction):** Implement primarily in **Rust**.
*   **AI/RAG Orchestration & Desktop App (UI):** Implement primarily in **Python** (for RAG) and **Tauri** (for desktop app).
*   **Containerization:** Utilize **Docker Compose** for all services (`db`, `ingestion_service`, `rag_service`, `desktop_app`).
*   **Database:** **PostgreSQL 16** with the **pgvector** extension for both metadata and dense vector storage.
*   **Email Parsing:** Use Rust `mail-parser` and `mbox` crates. HTML to plain text conversion using `html2text`. Signature stripping is a critical cleaning step.
*   **Embedding Models:** Prioritize local models (`multilingual-e5-large` or `bge-m3`) via a dedicated Python FastAPI service.
*   **LLMs:** Integrate with local LLMs (e.g., Llama-3 8B via Ollama). Cloud LLMs (GPT-4o) are an option but local is preferred for air-gapped needs.
*   **RAG Framework:** Use LangChain/LlamaIndex in Python for prompt composition and retrieval.

## 4. Compliance and Governance Mandates

**Directive:** Strict compliance with GDPR and EU AI Act is non-negotiable:
*   **GDPR:** Implement **pseudonymisation** of personal data *before* sending to any cloud services (e.g., cloud LLMs). Ensure data minimization.
*   **EU AI Act (Limited-Risk):**
    *   Maintain the **Human-in-the-Loop (HITL)** design. Automated email sending is **prohibited**.
    *   **Log all generated drafts and inputs** for a minimum of 6 months.
    *   Account for future **bias/safety evaluations** and **risk assessment reporting**.

## 5. Development Methodology

**Directive:** Follow an incremental, step-by-step development process:
*   **Small Chunks:** Each task must be broken into the smallest viable, testable units.
*   **Build Incrementally:** Each step must build upon previously completed and verified components. Avoid large jumps in complexity.
*   **No Orphaned Code:** All generated code must be integrated and wired into the existing system.
*   **Testing:** Implement unit, integration, and end-to-end tests for all components.
*   **No Hallucination:** All implementation details, technology choices, and architectural decisions must directly derive from the `spec.md` and `todo.md` documents. If a detail is missing, **request clarification**; do not invent.

## 6. Project Files and Structure

**Directive:** Maintain the specified project structure:
*   `email_assistant/` (root)
    *   `ingestion_service/` (Rust project)
    *   `rag_service/` (Python project)
    *   `desktop_app/` (Tauri project)
    *   `db/` (PostgreSQL Docker context)
    *   `docker-compose.yml`
    *   `spec.md` (functional and non-functional requirements)
    *   `todo.md` (detailed blueprint and LLM prompts)
    *   `strict_guidelines.md` (this document)

## 7. LLM Prompt Generation Guidelines

**Directive:** When generating LLM prompts for code generation:
*   **Specificity:** Prompts must be highly specific, detailing exact file paths, function signatures, and dependencies.
*   **Context:** Include relevant code snippets or file contents as context for the LLM.
*   **Persona:** Assign an appropriate expert persona (e.g., "You are an expert Rust developer.").
*   **Incremental:** Each prompt should target a single, well-defined step from the `todo.md` blueprint.
*   **Wiring:** Ensure prompts include instructions for integrating the newly generated code with existing components.
*   **Testing:** Encourage the LLM to include unit tests for the generated code where appropriate.

By adhering to these strict guidelines, we will ensure the successful and compliant development of the AI-Assisted Email Response System.