-- AI-Assisted Email Response System - Initial Database Schema
-- Migration 001: Create initial tables for email storage and vector embeddings (SQLite)

-- Create messages table for email metadata and content
CREATE TABLE IF NOT EXISTS messages (
    id TEXT PRIMARY KEY DEFAULT (lower(hex(randomblob(4))) || '-' || lower(hex(randomblob(2))) || '-4' || substr(lower(hex(randomblob(2))),2) || '-' || substr('89ab',abs(random()) % 4 + 1, 1) || substr(lower(hex(randomblob(2))),2) || '-' || lower(hex(randomblob(6)))),
    subject TEXT,
    from_address TEXT,
    to_addresses TEXT, -- JSON array of recipient addresses
    sent_date TEXT, -- ISO 8601 datetime string
    plain_text_body_raw TEXT,
    html_body_raw TEXT,
    cleaned_plain_text_body TEXT,
    file_path TEXT, -- Original file path for reference
    created_at TEXT DEFAULT (datetime('now')),
    updated_at TEXT DEFAULT (datetime('now'))
);

-- Create embeddings table for vector storage
CREATE TABLE IF NOT EXISTS embeddings (
    id TEXT PRIMARY KEY DEFAULT (lower(hex(randomblob(4))) || '-' || lower(hex(randomblob(2))) || '-4' || substr(lower(hex(randomblob(2))),2) || '-' || substr('89ab',abs(random()) % 4 + 1, 1) || substr(lower(hex(randomblob(2))),2) || '-' || lower(hex(randomblob(6)))),
    message_id TEXT NOT NULL REFERENCES messages(id) ON DELETE CASCADE,
    embedding TEXT NOT NULL, -- JSON array of floats
    embedding_model TEXT NOT NULL DEFAULT 'sentence-transformers/all-MiniLM-L6-v2',
    created_at TEXT DEFAULT (datetime('now'))
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_messages_from_address ON messages(from_address);
CREATE INDEX IF NOT EXISTS idx_messages_sent_date ON messages(sent_date);
CREATE INDEX IF NOT EXISTS idx_messages_created_at ON messages(created_at);
CREATE INDEX IF NOT EXISTS idx_embeddings_message_id ON embeddings(message_id);

-- Create trigger to automatically update updated_at
CREATE TRIGGER IF NOT EXISTS update_messages_updated_at
    AFTER UPDATE ON messages
    FOR EACH ROW
BEGIN
    UPDATE messages SET updated_at = datetime('now') WHERE id = NEW.id;
END;

-- Create view for messages with embeddings
CREATE VIEW IF NOT EXISTS messages_with_embeddings AS
SELECT
    m.*,
    e.embedding,
    e.embedding_model,
    e.created_at as embedding_created_at
FROM messages m
LEFT JOIN embeddings e ON m.id = e.message_id;
