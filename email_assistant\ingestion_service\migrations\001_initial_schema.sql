-- AI-Assisted Email Response System - Initial Database Schema
-- Migration 001: Create initial tables for email storage and vector embeddings

-- Enable pgvector extension
CREATE EXTENSION IF NOT EXISTS vector;

-- Create messages table for email metadata and content
CREATE TABLE IF NOT EXISTS messages (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    subject TEXT,
    from_address TEXT,
    to_addresses TEXT[], -- Array of recipient addresses
    sent_date TIMESTAMPTZ,
    plain_text_body_raw TEXT,
    html_body_raw TEXT,
    cleaned_plain_text_body TEXT,
    file_path TEXT, -- Original file path for reference
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create embeddings table for vector storage
CREATE TABLE IF NOT EXISTS embeddings (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    message_id UUID NOT NULL REFERENCES messages(id) ON DELETE CASCADE,
    embedding vector(1024), -- Assuming 1024-dimensional embeddings (adjust based on model)
    embedding_model TEXT NOT NULL DEFAULT 'multilingual-e5-large',
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_messages_from_address ON messages(from_address);
CREATE INDEX IF NOT EXISTS idx_messages_sent_date ON messages(sent_date);
CREATE INDEX IF NOT EXISTS idx_messages_created_at ON messages(created_at);
CREATE INDEX IF NOT EXISTS idx_embeddings_message_id ON embeddings(message_id);

-- Create vector similarity search index (using HNSW for approximate nearest neighbor)
CREATE INDEX IF NOT EXISTS idx_embeddings_vector_cosine ON embeddings 
USING hnsw (embedding vector_cosine_ops);

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger to automatically update updated_at
CREATE TRIGGER update_messages_updated_at 
    BEFORE UPDATE ON messages 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Create view for messages with embeddings
CREATE VIEW messages_with_embeddings AS
SELECT 
    m.*,
    e.embedding,
    e.embedding_model,
    e.created_at as embedding_created_at
FROM messages m
LEFT JOIN embeddings e ON m.id = e.message_id;

-- Insert sample data for testing (optional)
-- INSERT INTO messages (subject, from_address, to_addresses, cleaned_plain_text_body) 
-- VALUES ('Test Email', '<EMAIL>', ARRAY['<EMAIL>'], 'This is a test email body.');

COMMENT ON TABLE messages IS 'Stores parsed email messages with metadata and content';
COMMENT ON TABLE embeddings IS 'Stores vector embeddings for semantic search';
COMMENT ON COLUMN embeddings.embedding IS 'Vector embedding for semantic similarity search';
COMMENT ON INDEX idx_embeddings_vector_cosine IS 'HNSW index for fast cosine similarity search';
