[package]
name = "ingestion_service"
version = "0.1.0"
edition = "2024"

[[bin]]
name = "ingestion_service"
path = "src/main.rs"

[lib]
name = "ingestion_service"
path = "src/lib.rs"

[dependencies]
html2text = "0.5"
tokio = { version = "1.37", features = ["full"] }
sqlx = { version = "0.7", features = ["runtime-tokio-rustls", "postgres", "uuid", "macros", "chrono"] }
uuid = { version = "1.8", features = ["v4", "serde"] }
chrono = { version = "0.4", features = ["serde"] }
serde = { version = "1.0", features = ["derive"] }
reqwest = { version = "0.11", features = ["json"] }
dotenv = "0.15"
tempfile = "3.8"
